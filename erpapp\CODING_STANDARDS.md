# معايير البرمجة والتنسيق الموحد لنظام ERP

## 1. هيكل الملفات والمجلدات

### هيكل الوحدات (Modules)
```
App/Modules/
├── ModuleName/
│   ├── Controllers/
│   │   ├── EntityController.php
│   │   └── EntityGroupController.php
│   ├── Models/
│   │   ├── Entity.php
│   │   └── EntityGroup.php
│   └── Views/
│       ├── entity/
│       │   ├── index.php
│       │   ├── create.php
│       │   ├── edit.php
│       │   ├── show.php
│       │   ├── mixed.php
│       │   ├── dashboard.php
│       │   └── form_template.php
│       └── entity_groups/
│           ├── index.php
│           ├── create.php
│           └── edit.php
```

## 2. تسمية الملفات والكلاسات

### الوحدات الأساسية
- **المشتريات (Purchases)**: `suppliers` (الموردين)
- **المبيعات (Sales)**: `customers` (العملاء)
- **المخزون (Inventory)**: `items` (الأصناف)
- **المحاسبة (Accounting)**: `accounts` (الحسابات)

### تسمية الكلاسات
```php
// Controllers
class SupplierController extends BaseController
class CustomerController extends BaseController

// Models
class Supplier extends BaseModel
class Customer extends BaseModel
```

## 3. حقول قاعدة البيانات

### الحقول العامة (G_*)
- `G_name_ar`: الاسم بالعربية
- `G_name_en`: الاسم بالإنجليزية
- `G_phone`: الهاتف الثابت
- `G_mobile`: الهاتف الجوال
- `G_website`: الموقع الإلكتروني
- `G_notes`: الملاحظات العامة
- `G_status`: الحالة (active, inactive, suspended, draft)

### حقول الموردين (S_*)
- `S_company_name`: اسم الشركة
- `S_contact_person`: الشخص المسؤول
- `S_email`: البريد الإلكتروني
- `S_tax_number`: الرقم الضريبي
- `S_commercial_register`: السجل التجاري
- `S_payment_terms`: شروط الدفع (بالأيام)
- `S_credit_limit`: الحد الائتماني
- `S_discount_rate`: معدل الخصم
- `S_delivery_time`: وقت التسليم
- `S_minimum_order`: الحد الأدنى للطلب
- `S_currency`: العملة
- `S_rating`: التقييم
- `S_license_number`: رقم الترخيص
- `S_license_expiry`: تاريخ انتهاء الترخيص
- `S_establishment_date`: تاريخ التأسيس
- `S_legal_form`: الشكل القانوني
- `S_internal_notes`: ملاحظات داخلية
- `S_special_instructions`: تعليمات خاصة

### حقول العملاء (C_*)
- `C_email`: البريد الإلكتروني
- `C_tax_number`: الرقم الضريبي
- `C_commercial_register`: السجل التجاري
- `C_credit_limit`: الحد الائتماني
- `C_payment_terms`: شروط الدفع (بالأيام)
- `C_discount_rate`: معدل الخصم
- `C_customer_type`: نوع العميل (individual, company, government, other)
- `C_price_list`: قائمة الأسعار
- `C_sales_rep_id`: مندوب المبيعات
- `C_territory`: المنطقة
- `C_industry`: الصناعة
- `C_source`: مصدر العميل
- `C_rating`: التقييم

## 4. معايير البرمجة

### التعليقات
```php
/**
 * وصف الدالة بالعربية
 */
public function functionName()
{
    // تعليق سطر واحد بالعربية
}
```

### أسماء المتغيرات والدوال
```php
// استخدام camelCase للمتغيرات والدوال
$customerData = [];
$supplierModel = new Supplier();

// استخدام PascalCase للكلاسات
class CustomerController
```

### معالجة الأخطاء
```php
try {
    // الكود الرئيسي
} catch (Exception $e) {
    flash('error', $e->getMessage());
    redirect(base_url('module/entity'));
}
```

## 5. قواعد قاعدة البيانات

### الجداول الأساسية
- `concerned_entities`: الكيانات المعنية (موردين، عملاء)
- `entity_groups`: مجموعات الكيانات
- `entity_addresses`: عناوين الكيانات
- `entity_bank_accounts`: الحسابات البنكية للكيانات

### العلاقات
```sql
-- ربط الكيانات بالمجموعات
LEFT JOIN entity_groups g ON s.group_id = g.group_number 
    AND g.company_id = s.company_id AND g.section_id = s.section_id
```

## 6. ملفات العرض (Views)

### استخدام القوالب الموحدة
```php
// في ملفات create.php و edit.php
include __DIR__ . '/form_template.php';

render_form_page([
    'title' => $title,
    'module' => 'module_name',
    'entity' => 'entity_name',
    'form' => [
        'tabs' => $form_tabs
    ]
]);
```

### تعريف الأعمدة في الجداول
```php
[
    'field' => 'group_name',
    'title' => 'المجموعة',
    'type' => 'badge',
    'sortable' => true,
    'data_type' => 'text',
    'default_value' => 'غير محدد'
]
```

## 7. التحقق من البيانات

### التحقق الأساسي
```php
private function validateEntityData($data, $excludeEntityNumber = null)
{
    $errors = [];
    
    if (empty($data['G_name_ar'])) {
        $errors[] = 'اسم الكيان بالعربية مطلوب';
    }
    
    // المزيد من التحققات...
    
    if (!empty($errors)) {
        throw new Exception(implode('<br>', $errors));
    }
    
    return $validatedData;
}
```

## 8. الأمان

### حماية SQL Injection
```php
$sql = "SELECT * FROM table WHERE field = ?";
$stmt = $this->db->prepare($sql);
$stmt->execute([$value]);
```

### التحقق من الصلاحيات
```php
if (!is_logged_in()) {
    redirect(base_url('login'));
}

$user = current_user();
if (!$user || !$user['current_company_id']) {
    flash('error', 'يجب تحديد شركة حالية');
    redirect(base_url('companies'));
}
```

## 9. الرسائل والإشعارات

### استخدام Flash Messages
```php
flash('success', 'تم الحفظ بنجاح');
flash('error', 'حدث خطأ أثناء الحفظ');
flash('warning', 'تحذير: البيانات غير مكتملة');
flash('info', 'معلومة: لا توجد تغييرات');
```

## 10. التوثيق

### توثيق الدوال
```php
/**
 * إنشاء كيان جديد
 * 
 * @param array $data بيانات الكيان
 * @return int|false رقم الكيان الجديد أو false في حالة الفشل
 */
public function create($data)
{
    // تنفيذ الدالة
}
```
