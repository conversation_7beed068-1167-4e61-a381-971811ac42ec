<?php
namespace App\Modules\Sales\Controllers;

use App\Modules\Sales\Models\Customer;
use App\Modules\Sales\Models\CustomerGroup;
use Exception;
use PDO;

/**
 * SaleController - متحكم لوحة تحكم المبيعات
 */
class SaleController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * Customer model
     */
    protected $customerModel;

    /**
     * CustomerGroup model
     */
    protected $customerGroupModel;

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->customerModel = new Customer();
        $this->customerGroupModel = new CustomerGroup();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash('sales_error', 'يجب تحديد شركة حالية للوصول إلى المبيعات', 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * لوحة تحكم المبيعات
     */
    public function index()
    {
        $company_id = current_user()['current_company_id'];

        // الحصول على الإحصائيات
        $customerStats = $this->customerModel->getStats($company_id);
        $groupStats = $this->customerGroupModel->getStats($company_id);

        // الحصول على آخر العملاء المضافين
        $recentCustomers = $this->customerModel->getByCompany($company_id, [
            'limit' => 5,
            'order_by' => 's.created_at DESC'
        ]);

        // إحصائيات عامة
        $stats = [
            'total_customers' => $customerStats['total_customers'],
            'active_customers' => $customerStats['active_customers'],
            'total_groups' => $groupStats['total_groups'],
            'inactive_customers' => $customerStats['total_customers'] - $customerStats['active_customers']
        ];

        // عرض لوحة التحكم
        $data = [
            'title' => 'لوحة تحكم المبيعات',
            'stats' => $stats,
            'recentCustomers' => $recentCustomers,
            'breadcrumb' => [
                ['title' => 'المبيعات', 'active' => true]
            ]
        ];

        view('Sales::dashboard/index', $data);
    }

    /**
     * لوحة تحكم المبيعات (نفس index)
     */
    public function dashboard()
    {
        return $this->index();
    }
}