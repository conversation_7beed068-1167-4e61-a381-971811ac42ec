<?php
namespace App\Modules\Sales\Controllers;

use App\Modules\Sales\Models\CustomerGroup;
use Exception;
use PDO;

/**
 * CustomerGroupController - متحكم مجموعات العملاء
 */
class CustomerGroupController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * CustomerGroup model
     */
    protected $customerGroupModel;

    /**
     * Module and entity configuration
     */
    protected $module = 'sales';
    protected $entity = 'customer-groups';
    protected $moduleTitle = 'المبيعات';
    protected $entityTitle = 'مجموعات العملاء';
    protected $entityTitleSingular = 'مجموعة العملاء';

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->customerGroupModel = new CustomerGroup();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash($this->entity . '_error', 'يجب تحديد شركة حالية للوصول إلى ' . $this->entityTitle, 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض قائمة مجموعات العملاء
     */
    public function index()
    {
        // استخدام الدالة الموحدة لمعالجة index
        $data = handle_datatable_index([
            'filter_name' => $this->entity,
            'default_filters' => ['is_default' => ''],
            'filter_fields' => ['search', 'is_default'],
            'model' => $this->customerGroupModel,
            'title' => $this->entityTitle,
            'data_key' => 'customerGroups',
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'active' => true]
            ]
        ]);

        view('Sales::customer-groups/index', $data);
    }

    /**
     * عرض صفحة إضافة مجموعة عملاء جديدة
     */
    public function create()
    {
        $data = [
            'title' => 'إضافة ' . $this->entityTitleSingular . ' جديدة',
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'إضافة ' . $this->entityTitleSingular, 'active' => true]
            ]
        ];

        view('Sales::customer-groups/create', $data);
    }

    /**
     * حفظ مجموعة عملاء جديدة
     */
    public function store()
    {
        try {
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            $this->customerGroupModel->beginTransaction();

            // التحقق من صحة البيانات
            $validatedData = $this->validateCustomerGroupData($_POST);
            $validatedData['company_id'] = $company_id;
            $validatedData['created_by'] = $user_id;

            // إنشاء المجموعة
            $groupNumber = $this->customerGroupModel->create($validatedData);

            if ($groupNumber) {
                // إذا تم تعيين هذه المجموعة كافتراضية، تحديث الإعدادات
                if ($validatedData['is_default'] == 1) {
                    $this->customerGroupModel->setAsDefault($groupNumber, $company_id);
                }

                $this->customerGroupModel->commit();

                flash('success', 'تم إنشاء ' . $this->entityTitleSingular . ' بنجاح');
                redirect(base_url($this->module . '/' . $this->entity));
            } else {
                $this->customerGroupModel->rollback();
                flash('error', 'حدث خطأ أثناء إنشاء ' . $this->entityTitleSingular);
                redirect(base_url($this->module . '/' . $this->entity . '/create'));
            }

        } catch (Exception $e) {
            $this->customerGroupModel->rollback();
            flash('error', $e->getMessage());
            redirect(base_url($this->module . '/' . $this->entity . '/create'));
        }
    }

    /**
     * عرض تفاصيل مجموعة عملاء
     */
    public function show()
    {
        $group_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $customerGroup = $this->customerGroupModel->getByNumber($group_number, $company_id);

        if (!$customerGroup) {
            flash('error', $this->entityTitleSingular . ' غير موجودة');
            redirect(base_url($this->module . '/' . $this->entity));
        }

        $data = [
            'title' => 'عرض ' . $this->entityTitleSingular . ' - ' . $customerGroup['name_ar'],
            'customerGroup' => $customerGroup,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'عرض ' . $this->entityTitleSingular, 'active' => true]
            ]
        ];

        view('Sales::customer-groups/show', $data);
    }

    /**
     * عرض صفحة تعديل مجموعة عملاء
     */
    public function edit()
    {
        $group_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $customerGroup = $this->customerGroupModel->getByNumber($group_number, $company_id);

        if (!$customerGroup) {
            flash('error', $this->entityTitleSingular . ' غير موجودة');
            redirect(base_url($this->module . '/' . $this->entity));
        }

        $data = [
            'title' => 'تعديل ' . $this->entityTitleSingular . ' - ' . $customerGroup['name_ar'],
            'customerGroup' => $customerGroup,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'تعديل ' . $this->entityTitleSingular, 'active' => true]
            ]
        ];

        view('Sales::customer-groups/edit', $data);
    }

    /**
     * تحديث مجموعة عملاء
     */
    public function update()
    {
        try {
            $group_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المجموعة
            $customerGroup = $this->customerGroupModel->getByNumber($group_number, $company_id);
            if (!$customerGroup) {
                flash('error', $this->entityTitleSingular . ' غير موجودة');
                redirect(base_url($this->module . '/' . $this->entity));
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validateCustomerGroupData($_POST, $group_number);

            // فحص التغييرات
            if (!$this->hasDataChanged($customerGroup, $validatedData)) {
                flash('info', 'لا توجد تغييرات لحفظها');
                redirect(base_url($this->module . '/' . $this->entity . '/' . $group_number . '/edit'));
                return;
            }

            $validatedData['updated_by'] = current_user()['UserID'];

            $db = $this->customerGroupModel->getDb();
            $db->beginTransaction();

            try {
                // إذا تم تعيين هذه المجموعة كافتراضية، إزالة الافتراضية من المجموعات الأخرى
                if ($validatedData['is_default'] == 1) {
                    $this->customerGroupModel->setAsDefault($group_number, $company_id);
                    // إزالة is_default من البيانات لأنه تم معالجته بشكل منفصل
                    unset($validatedData['is_default']);
                }

                // تحديث المجموعة
                $result = $this->customerGroupModel->update($group_number, $validatedData, $company_id);

                if (!$result) {
                    throw new Exception('فشل في تحديث البيانات الأساسية لـ' . $this->entityTitleSingular);
                }

                $db->commit();

                flash('success', 'تم تحديث ' . $this->entityTitleSingular . ' بنجاح - رقم: ' . $group_number);
                redirect(base_url($this->module . '/' . $this->entity));

            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect(base_url($this->module . '/' . $this->entity . '/' . $this->params['id'] . '/edit'));
        }
    }

    /**
     * حذف مجموعة عملاء
     */
    public function delete()
    {
        try {
            $group_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المجموعة
            $customerGroup = $this->customerGroupModel->getByNumber($group_number, $company_id);
            if (!$customerGroup) {
                flash('error', $this->entityTitleSingular . ' غير موجودة');
                redirect(base_url($this->module . '/' . $this->entity));
            }

            // حذف المجموعة
            $result = $this->customerGroupModel->delete($group_number, $company_id);

            if ($result) {
                flash('success', 'تم حذف ' . $this->entityTitleSingular . ' بنجاح');
            } else {
                flash('error', 'حدث خطأ أثناء حذف ' . $this->entityTitleSingular);
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        redirect(base_url($this->module . '/' . $this->entity));
    }

    /**
     * تطبيق فلاتر مجموعات العملاء
     */
    public function applyFilters()
    {
        handle_apply_filters($this->entity, ['is_default'], base_url($this->module . '/' . $this->entity));
    }

    /**
     * مسح فلاتر مجموعات العملاء
     */
    public function clearFilters()
    {
        handle_clear_filters($this->entity, base_url($this->module . '/' . $this->entity));
    }

    /**
     * عرض إحصائيات مجموعات العملاء
     */
    public function stats()
    {
        $company_id = current_user()['current_company_id'];

        $stats = $this->customerGroupModel->getStats($company_id);

        $data = [
            'title' => 'إحصائيات ' . $this->entityTitle,
            'stats' => $stats,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'الإحصائيات', 'active' => true]
            ]
        ];

        view('Sales::customer-groups/stats', $data);
    }

    /**
     * فحص ما إذا كانت البيانات تغيرت
     */
    private function hasDataChanged($originalData, $newData)
    {
        // الحقول التي نريد مقارنتها
        $fieldsToCompare = ['name_ar', 'name_en', 'is_default'];

        foreach ($fieldsToCompare as $field) {
            $originalValue = $originalData[$field] ?? '';
            $newValue = $newData[$field] ?? '';

            // تنظيف القيم للمقارنة
            $cleanOriginal = trim((string)$originalValue);
            $cleanNew = trim((string)$newValue);

            // معالجة القيم الفارغة والـ null
            if ($cleanOriginal === '' && $cleanNew === '') continue;
            if ($cleanOriginal === '' && $cleanNew === '0') continue;
            if ($cleanOriginal === '0' && $cleanNew === '') continue;

            // مقارنة القيم
            if ($cleanOriginal !== $cleanNew) {
                return true; // يوجد تغيير
            }
        }

        return false; // لا توجد تغييرات
    }

    /**
     * التحقق من صحة بيانات مجموعة العملاء
     */
    private function validateCustomerGroupData($data, $excludeGroupNumber = null)
    {
        $errors = [];

        // التحقق من الاسم العربي
        if (empty($data['name_ar'])) {
            $errors[] = 'اسم المجموعة بالعربية مطلوب';
        }

        // التحقق من عدم تكرار الاسم
        if (!empty($data['name_ar'])) {
            $company_id = current_user()['current_company_id'];
            $existing_group = $this->customerGroupModel->findByNameAndCompany($data['name_ar'], $company_id);

            if ($existing_group && (!$excludeGroupNumber || $existing_group['group_number'] != $excludeGroupNumber)) {
                $errors[] = 'اسم المجموعة موجود بالفعل في الشركة الحالية';
            }
        }

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // إعداد البيانات
        return [
            'name_ar' => trim($data['name_ar']),
            'name_en' => trim($data['name_en'] ?? ''),
            'is_default' => isset($data['is_default']) && $data['is_default'] == '1' ? 1 : 0
        ];
    }
}
