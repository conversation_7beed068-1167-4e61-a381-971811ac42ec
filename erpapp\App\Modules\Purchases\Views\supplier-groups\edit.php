<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات
$actions = [
    [
        'type' => 'secondary',
        'url' => 'purchases/supplier-groups',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'info',
        'url' => 'purchases/supplier-groups/' . $supplierGroup['group_number'],
        'icon' => 'fas fa-eye',
        'text' => 'عرض المجموعة'
    ],
    [
        'type' => 'primary',
        'form' => 'supplierGroupForm',
        'icon' => 'fas fa-save',
        'text' => 'حفظ التغييرات',
        'submit' => true
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'مجموعات الموردين', 'url' => 'purchases/supplier-groups'],
    ['title' => 'تعديل مجموعة', 'active' => true]
];

// استخدام النظام الموحد
render_form_page([
    'title' => $title ?? 'تعديل مجموعة الموردين - ' . $supplierGroup['name_ar'],
    'module' => 'purchases',
    'entity' => 'supplier-groups',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'form_data' => $supplierGroup ?? [], // بيانات المجموعة الحالية

    // النموذج بالتبويبات الديناميكية
    'form' => [
      'action' => base_url('purchases/supplier-groups/' . $supplierGroup['group_number'] . '/update'),
        'method' => 'POST',
        'id' => 'supplierGroupForm',
          'tabs' => $form_tabs,
        'attributes' => [
            'data-track-changes' => 'true'
        ]
    ]
]);
?>