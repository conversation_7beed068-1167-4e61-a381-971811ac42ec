<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات للعرض فقط
$actions = [
    [
        'type' => 'secondary',
        'url' => 'purchases/supplier-groups',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'url' => 'purchases/supplier-groups/' . ($supplierGroup['group_number'] ?? '') . '/edit',
        'icon' => 'fas fa-edit',
        'text' => 'تعديل المجموعة'
    ],
    [
        'type' => 'danger',
        'onclick' => 'confirmDelete(' . ($supplierGroup['group_number'] ?? 0) . ')',
        'icon' => 'fas fa-trash',
        'text' => 'حذف المجموعة'
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'مجموعات الموردين', 'url' => 'purchases/supplier-groups'],
    ['title' => $supplierGroup['name_ar'] ?? 'عرض المجموعة', 'active' => true]
];

// استخدام النظام الموحد في وضع القراءة فقط
render_form_page([
    'title' => $title ?? 'عرض مجموعة الموردين - ' . ($supplierGroup['name_ar'] ?? ''),
    'module' => 'purchases',
    'entity' => 'supplier-groups',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'form_data' => $supplierGroup ?? [], // بيانات المجموعة الحالية
    'readonly' => true, // وضع القراءة فقط
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => '#', // لا يوجد action في وضع العرض
        'method' => 'GET',
        'id' => 'supplierGroupForm',
        'tabs' => $form_tabs
    ]
]);
?>

<script>
function confirmDelete(groupNumber) {
    if (confirm('هل أنت متأكد من حذف مجموعة الموردين؟\nلا يمكن التراجع عن هذا الإجراء.')) {
        // إنشاء نموذج مخفي للحذف
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url('purchases/supplier-groups/') ?>' + groupNumber + '/delete';
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
