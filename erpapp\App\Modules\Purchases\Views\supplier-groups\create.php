<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات
$actions = [
    [
        'type' => 'secondary',
        'url' => 'purchases/supplier-groups',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'form' => 'supplierGroupForm',
        'icon' => 'fas fa-save',
        'text' => 'حفظ المجموعة',
        'submit' => true
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'مجموعات الموردين', 'url' => 'purchases/supplier-groups'],
    ['title' => 'إضافة مجموعة', 'active' => true]
];

// استخدام النظام الموحد
render_form_page([
    'title' => $title ?? 'إضافة مجموعة موردين جديدة',
    'module' => 'purchases',
    'entity' => 'supplier-groups',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => base_url('purchases/supplier-groups/store'),
        'method' => 'POST',
        'id' => 'supplierGroupForm',
        'tabs' => $form_tabs
    ]
]);
?>
