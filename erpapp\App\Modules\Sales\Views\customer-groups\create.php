<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات
$actions = [
    [
        'type' => 'secondary',
        'url' => 'sales/customer-groups',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'form' => 'customerGroupForm',
        'icon' => 'fas fa-save',
        'text' => 'حفظ المجموعة',
        'submit' => true
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المبيعات', 'url' => 'sales'],
    ['title' => 'مجموعات العملاء', 'url' => 'sales/customer-groups'],
    ['title' => 'إضافة مجموعة', 'active' => true]
];

// استخدام النظام الموحد
render_form_page([
    'title' => $title ?? 'إضافة مجموعة عملاء جديدة',
    'module' => 'sales',
    'entity' => 'customer-groups',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => base_url('sales/customer-groups/store'),
        'method' => 'POST',
        'id' => 'customerGroupForm',
        'tabs' => $form_tabs
    ]
]);
?>
