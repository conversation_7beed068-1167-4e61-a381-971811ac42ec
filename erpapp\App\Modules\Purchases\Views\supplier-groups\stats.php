<?php
/**
 * صفحة إحصائيات مجموعات الموردين
 */

// إعداد الإجراءات
$actions = [
    [
        'type' => 'secondary',
        'url' => 'purchases/supplier-groups',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'url' => 'purchases/supplier-groups/create',
        'icon' => 'fas fa-plus',
        'text' => 'إضافة مجموعة جديدة'
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'مجموعات الموردين', 'url' => 'purchases/supplier-groups'],
    ['title' => 'الإحصائيات', 'active' => true]
];

// إعداد بطاقات الإحصائيات
$stats_cards = [
    [
        'title' => 'إجمالي المجموعات',
        'value' => $stats['total_groups'] ?? 0,
        'icon' => 'fas fa-layer-group',
        'color' => 'primary',
        'description' => 'العدد الكلي لمجموعات الموردين'
    ],
    [
        'title' => 'المجموعات الافتراضية',
        'value' => $stats['default_groups'] ?? 0,
        'icon' => 'fas fa-star',
        'color' => 'warning',
        'description' => 'المجموعات المحددة كافتراضية'
    ],
    [
        'title' => 'المجموعات النشطة',
        'value' => $stats['active_groups'] ?? 0,
        'icon' => 'fas fa-check-circle',
        'color' => 'success',
        'description' => 'المجموعات التي تحتوي على موردين'
    ],
    [
        'title' => 'المجموعات الفارغة',
        'value' => $stats['empty_groups'] ?? 0,
        'icon' => 'fas fa-folder-open',
        'color' => 'secondary',
        'description' => 'المجموعات التي لا تحتوي على موردين'
    ]
];

// إعداد الرسوم البيانية
$charts_config = [
    [
        'id' => 'groupsChart',
        'title' => 'توزيع المجموعات',
        'type' => 'doughnut',
        'data' => [
            'labels' => ['مجموعات نشطة', 'مجموعات فارغة'],
            'datasets' => [[
                'data' => [
                    $stats['active_groups'] ?? 0,
                    $stats['empty_groups'] ?? 0
                ],
                'backgroundColor' => ['#28a745', '#6c757d']
            ]]
        ]
    ],
    [
        'id' => 'suppliersPerGroupChart',
        'title' => 'عدد الموردين لكل مجموعة',
        'type' => 'bar',
        'data' => [
            'labels' => array_column($stats['groups_with_suppliers'] ?? [], 'name_ar'),
            'datasets' => [[
                'label' => 'عدد الموردين',
                'data' => array_column($stats['groups_with_suppliers'] ?? [], 'suppliers_count'),
                'backgroundColor' => '#007bff'
            ]]
        ]
    ]
];

// استخدام النظام الموحد لعرض الإحصائيات
render_stats_page([
    'title' => $title ?? 'إحصائيات مجموعات الموردين',
    'module' => 'purchases',
    'entity' => 'supplier-groups',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'stats_cards' => $stats_cards,
    'charts' => $charts_config,
    'additional_content' => [
        [
            'type' => 'table',
            'title' => 'تفاصيل المجموعات',
            'data' => $stats['groups_details'] ?? [],
            'columns' => [
                ['field' => 'name_ar', 'title' => 'اسم المجموعة'],
                ['field' => 'suppliers_count', 'title' => 'عدد الموردين'],
                ['field' => 'is_default', 'title' => 'افتراضية', 'type' => 'badge'],
                ['field' => 'created_at', 'title' => 'تاريخ الإنشاء', 'type' => 'date']
            ]
        ]
    ]
]);
?>
