<?php
/**
 * مساعد تحميل CSS الذكي
 */

/**
 * تحديد الوحدة الحالية من URL
 *
 * @return string|null
 */
function get_current_module() {
    $url = $_SERVER['REQUEST_URI'];

    // إزالة base path من URL
    $base_path = parse_url(APP_URL, PHP_URL_PATH);
    if ($base_path && strpos($url, $base_path) === 0) {
        $url = substr($url, strlen($base_path));
    }

    // إزالة query string
    if ($pos = strpos($url, '?')) {
        $url = substr($url, 0, $pos);
    }

    // إزالة trailing slash
    $url = trim($url, '/');

    // تحليل URL لاستخراج الوحدة
    $parts = explode('/', $url);

    if (empty($parts[0])) {
        return 'dashboard'; // الصفحة الرئيسية
    }

    // تحويل أسماء المسارات إلى أسماء الوحدات
    $module_mapping = [
        'login' => 'auth',
        'register' => 'auth',
        'forgot-password' => 'auth',
        'reset-password' => 'auth',
        'logout' => 'auth',
        'companies' => 'companies',
        'subscriptions' => 'subscriptions',
        'plans' => 'subscriptions',
        'payments' => 'subscriptions',
        'users' => 'users',
        'profile' => 'users',
        'settings' => 'users',
        'purchases' => 'purchases',
        'inventory' => 'inventory',
        'sales' => 'sales',
        'dashboard' => 'dashboard',
        '' => 'dashboard'
    ];

    $first_part = $parts[0];
    return $module_mapping[$first_part] ?? 'dashboard';
}

/**
 * تحديد الصفحة الحالية من URL
 *
 * @return string
 */
function get_current_page() {
    $url = $_SERVER['REQUEST_URI'];

    // إزالة base path من URL
    $base_path = parse_url(APP_URL, PHP_URL_PATH);
    if ($base_path && strpos($url, $base_path) === 0) {
        $url = substr($url, strlen($base_path));
    }

    // إزالة query string
    if ($pos = strpos($url, '?')) {
        $url = substr($url, 0, $pos);
    }

    // إزالة trailing slash
    $url = trim($url, '/');

    if (empty($url)) {
        return 'dashboard';
    }

    // تحليل URL لاستخراج الصفحة
    $parts = explode('/', $url);

    // تحويل المسار إلى اسم صفحة
    if (count($parts) == 1) {
        return $parts[0];
    } elseif (count($parts) >= 2) {
        return $parts[0] . '-' . $parts[1];
    }

    return 'dashboard';
}

/**
 * تحميل ملفات CSS المطلوبة للصفحة الحالية
 *
 * @return array قائمة بملفات CSS المطلوبة
 */
function get_required_css_files() {
    $module = get_current_module();
    $page = get_current_page();

    $css_files = [];

    // ملفات CSS الأساسية (تحمل في كل صفحة)
    $core_files = [
        'core/variables.css',
         'core/base.css',  // الأنماط العامة والأساسية
        'layout/sidebar.css',     // أنماط الشريط الجانبي
        'layout/topbar.css'  ,     // أنماط الشريط العلوي

         'components/tables.css',    // أنماط الشريط العلوي
            'components/forms.css' ,  // أنماط الشريط العلوي
              'components/cards.css' ,  // أنماط الشريط العلوي
               'components/buttons.css',       // أنماط الشريط العلوي
               'components/header.css',
                'components/modals.css',       // أنماط الشريط العلوي
                'components/import-export.css'  // أنماط الاستيراد والتصدير
    ];

    $css_files = array_merge($css_files, $core_files);

    // ملف CSS الخاص بالوحدة
    if ($module && file_exists(BASE_PATH . "/public/css/modules/{$module}.css")) {
        $css_files[] = "modules/{$module}.css";
    }

    // ملف CSS الخاص بالصفحة
    if ($page && file_exists(BASE_PATH . "/public/css/pages/{$page}.css")) {
        $css_files[] = "pages/{$page}.css";
    }

    // ملف الثيم (يتم تحديده حسب إعدادات المستخدم)
    $theme = get_user_theme();
    if ($theme && file_exists(BASE_PATH . "/public/css/themes/{$theme}.css")) {
        $css_files[] = "themes/{$theme}.css";
    }

    return $css_files;
}

/**
 * الحصول على ثيم المستخدم
 *
 * @return string
 */
function get_user_theme() {
    // التحقق من إعدادات المستخدم في قاعدة البيانات أو الجلسة
    if (isset($_SESSION['theme'])) {
        return $_SESSION['theme'];
    }

    // إعداد افتراضي
    return 'light';
}

/**
 * طباعة تاجات link لملفات CSS
 *
 * @return void
 */
function load_css_files() {
    $css_files = get_required_css_files();
    $base_url = rtrim(APP_URL, '/');

    echo "<!-- DEBUG: Loading CSS files -->\n";
    echo "<!-- DEBUG: Base URL: $base_url -->\n";
    echo "<!-- DEBUG: CSS files: " . implode(', ', $css_files) . " -->\n";

    foreach ($css_files as $file) {
        $file_path = BASE_PATH . "/public/css/{$file}";

        if (file_exists($file_path)) {
            $version = filemtime($file_path); // للتحكم في cache
            echo "<link rel=\"stylesheet\" href=\"{$base_url}/public/css/{$file}?v={$version}\">\n";
            echo "<!-- DEBUG: Loaded CSS: {$file} -->\n";
        } else {
            echo "<!-- DEBUG: CSS file not found: {$file} at {$file_path} -->\n";
        }
    }
}

/**
 * تحميل CSS مخصص للصفحة
 *
 * @param string $css_content محتوى CSS
 * @return void
 */
function inline_css($css_content) {
    echo "<style>\n{$css_content}\n</style>\n";
}

/**
 * تحميل CSS من ملف خارجي
 *
 * @param string $file_path مسار الملف
 * @return void
 */
function load_external_css($file_path) {
    if (file_exists($file_path)) {
        $version = filemtime($file_path);
        $base_url = rtrim(APP_URL, '/');
        $relative_path = str_replace(BASE_PATH . '/public/', '', $file_path);
        echo "<link rel=\"stylesheet\" href=\"{$base_url}/public/{$relative_path}?v={$version}\">\n";
    }
}

/**
 * تحديد ما إذا كانت الصفحة تحتاج RTL
 *
 * @return bool
 */
function is_rtl_page() {
    return current_lang() === 'ar';
}

/**
 * تحميل CSS للغة RTL إذا لزم الأمر
 *
 * @return void
 */
function load_rtl_css() {
    if (is_rtl_page()) {
        $rtl_file = BASE_PATH . '/public/css/core/rtl.css';
        if (file_exists($rtl_file)) {
            load_external_css($rtl_file);
        }
    }
}
