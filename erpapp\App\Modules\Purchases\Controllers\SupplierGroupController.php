<?php
namespace App\Modules\Purchases\Controllers;

use App\Modules\Purchases\Models\SupplierGroup;
use Exception;
use PDO;

/**
 * SupplierGroupController - متحكم مجموعات الموردين
 */
class SupplierGroupController
{
    /**
     * Route parameters
     */
    protected $params = [];

    /**
     * SupplierGroup model
     */
    protected $supplierGroupModel;

    /**
     * Module and entity configuration
     */
    protected $module = 'purchases';
    protected $entity = 'supplier-groups';
    protected $moduleTitle = 'المشتريات';
    protected $entityTitle = 'مجموعات الموردين';
    protected $entityTitleSingular = 'مجموعة الموردين';

    /**
     * Constructor
     */
    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierGroupModel = new SupplierGroup();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        // التحقق من وجود شركة حالية
        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash($this->entity . '_error', 'يجب تحديد شركة حالية للوصول إلى ' . $this->entityTitle, 'warning');
            redirect(base_url('companies'));
        }
    }

    /**
     * عرض قائمة مجموعات الموردين
     */
    public function index()
    {
        // استخدام الدالة الموحدة لمعالجة index
        $data = handle_datatable_index([
            'filter_name' => $this->entity,
            'default_filters' => ['is_default' => ''],
            'filter_fields' => ['search', 'is_default'],
            'model' => $this->supplierGroupModel,
            'title' => $this->entityTitle,
            'data_key' => 'supplierGroups',
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'active' => true]
            ]
        ]);

        view('Purchases::supplier-groups/index', $data);
    }

    /**
     * عرض صفحة إضافة مجموعة موردين جديدة
     */
    public function create()
    {
        $data = [
            'title' => 'إضافة ' . $this->entityTitleSingular . ' جديدة',
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'إضافة ' . $this->entityTitleSingular, 'active' => true]
            ]
        ];

        view('Purchases::supplier-groups/create', $data);
    }

    /**
     * حفظ مجموعة موردين جديدة
     */
    public function store()
    {
        try {
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            $this->supplierGroupModel->beginTransaction();

            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierGroupData($_POST);
            $validatedData['company_id'] = $company_id;
            $validatedData['created_by'] = $user_id;

            // إنشاء المجموعة
            $groupNumber = $this->supplierGroupModel->create($validatedData);

            if ($groupNumber) {
                // إذا تم تعيين هذه المجموعة كافتراضية، تحديث الإعدادات
                if ($validatedData['is_default'] == 1) {
                    $this->supplierGroupModel->setAsDefault($groupNumber, $company_id);
                }

                $this->supplierGroupModel->commit();

                flash('success', 'تم إنشاء ' . $this->entityTitleSingular . ' بنجاح');
                redirect(base_url($this->module . '/' . $this->entity));
            } else {
                $this->supplierGroupModel->rollback();
                flash('error', 'حدث خطأ أثناء إنشاء ' . $this->entityTitleSingular);
                redirect(base_url($this->module . '/' . $this->entity . '/create'));
            }

        } catch (Exception $e) {
            $this->supplierGroupModel->rollback();
            flash('error', $e->getMessage());
            redirect(base_url($this->module . '/' . $this->entity . '/create'));
        }
    }

    /**
     * عرض تفاصيل مجموعة موردين
     */
    public function show()
    {
        $group_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);

        if (!$supplierGroup) {
            flash('error', $this->entityTitleSingular . ' غير موجودة');
            redirect(base_url($this->module . '/' . $this->entity));
        }

        $data = [
            'title' => 'عرض ' . $this->entityTitleSingular . ' - ' . $supplierGroup['name_ar'],
            'supplierGroup' => $supplierGroup,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'عرض ' . $this->entityTitleSingular, 'active' => true]
            ]
        ];

        view('Purchases::supplier-groups/show', $data);
    }

    /**
     * عرض صفحة تعديل مجموعة موردين
     */
    public function edit()
    {
        $group_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);

        if (!$supplierGroup) {
            flash('error', $this->entityTitleSingular . ' غير موجودة');
            redirect(base_url($this->module . '/' . $this->entity));
        }

        $data = [
            'title' => 'تعديل ' . $this->entityTitleSingular . ' - ' . $supplierGroup['name_ar'],
            'supplierGroup' => $supplierGroup,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'تعديل ' . $this->entityTitleSingular, 'active' => true]
            ]
        ];

        view('Purchases::supplier-groups/edit', $data);
    }

    /**
     * تحديث مجموعة موردين
     */
    public function update()
    {
        try {
            $group_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المجموعة
            $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);
            if (!$supplierGroup) {
                flash('error', $this->entityTitleSingular . ' غير موجودة');
                redirect(base_url($this->module . '/' . $this->entity));
            }

            // التحقق من صحة البيانات
            $validatedData = $this->validateSupplierGroupData($_POST, $group_number);

            // فحص التغييرات
            if (!$this->hasDataChanged($supplierGroup, $validatedData)) {
                flash('info', 'لا توجد تغييرات لحفظها');
                redirect(base_url($this->module . '/' . $this->entity . '/' . $group_number . '/edit'));
                return;
            }

            $validatedData['updated_by'] = current_user()['UserID'];

            $db = $this->supplierGroupModel->getDb();
            $db->beginTransaction();

            try {
                // إذا تم تعيين هذه المجموعة كافتراضية، إزالة الافتراضية من المجموعات الأخرى
                if ($validatedData['is_default'] == 1) {
                    $this->supplierGroupModel->setAsDefault($group_number, $company_id);
                    // إزالة is_default من البيانات لأنه تم معالجته بشكل منفصل
                    unset($validatedData['is_default']);
                }

                // تحديث المجموعة
                $result = $this->supplierGroupModel->update($group_number, $validatedData, $company_id);

                if (!$result) {
                    throw new Exception('فشل في تحديث البيانات الأساسية لـ' . $this->entityTitleSingular);
                }

                $db->commit();

                flash('success', 'تم تحديث ' . $this->entityTitleSingular . ' بنجاح - رقم: ' . $group_number);
                redirect(base_url($this->module . '/' . $this->entity));

            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
            redirect(base_url($this->module . '/' . $this->entity . '/' . $this->params['id'] . '/edit'));
        }
    }

    /**
     * حذف مجموعة موردين
     */
    public function delete()
    {
        try {
            $group_number = $this->params['id'];
            $company_id = current_user()['current_company_id'];

            // التحقق من وجود المجموعة
            $supplierGroup = $this->supplierGroupModel->getByNumber($group_number, $company_id);
            if (!$supplierGroup) {
                flash('error', $this->entityTitleSingular . ' غير موجودة');
                redirect(base_url($this->module . '/' . $this->entity));
            }

            // حذف المجموعة
            $result = $this->supplierGroupModel->delete($group_number, $company_id);

            if ($result) {
                flash('success', 'تم حذف ' . $this->entityTitleSingular . ' بنجاح');
            } else {
                flash('error', 'حدث خطأ أثناء حذف ' . $this->entityTitleSingular);
            }

        } catch (Exception $e) {
            flash('error', $e->getMessage());
        }

        redirect(base_url($this->module . '/' . $this->entity));
    }

    /**
     * تطبيق فلاتر مجموعات الموردين (POST form submission)
     */
    public function applyFilters()
    {
        handle_apply_filters($this->entity, ['is_default'], base_url($this->module . '/' . $this->entity));
    }

    /**
     * مسح فلاتر مجموعات الموردين
     */
    public function clearFilters()
    {
        handle_clear_filters($this->entity, base_url($this->module . '/' . $this->entity));
    }

    /**
     * عرض إحصائيات مجموعات الموردين
     */
    public function stats()
    {
        $company_id = current_user()['current_company_id'];

        $stats = $this->supplierGroupModel->getStats($company_id);

        $data = [
            'title' => 'إحصائيات ' . $this->entityTitle,
            'stats' => $stats,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'الإحصائيات', 'active' => true]
            ]
        ];

        view('Purchases::supplier-groups/stats', $data);
    }

    /**
     * فحص ما إذا كانت البيانات تغيرت
     */
    private function hasDataChanged($originalData, $newData)
    {
        // الحقول التي نريد مقارنتها
        $fieldsToCompare = ['name_ar', 'name_en', 'is_default'];

        foreach ($fieldsToCompare as $field) {
            $originalValue = $originalData[$field] ?? '';
            $newValue = $newData[$field] ?? '';

            // تنظيف القيم للمقارنة
            $cleanOriginal = trim((string)$originalValue);
            $cleanNew = trim((string)$newValue);

            // معالجة القيم الفارغة والـ null
            if ($cleanOriginal === '' && $cleanNew === '') continue;
            if ($cleanOriginal === '' && $cleanNew === '0') continue;
            if ($cleanOriginal === '0' && $cleanNew === '') continue;

            // مقارنة القيم
            if ($cleanOriginal !== $cleanNew) {
                return true; // يوجد تغيير
            }
        }

        return false; // لا توجد تغييرات
    }

    /**
     * التحقق من صحة بيانات مجموعة الموردين
     */
    private function validateSupplierGroupData($data, $excludeGroupNumber = null)
    {
        $errors = [];

        // التحقق من الاسم العربي
        if (empty($data['name_ar'])) {
            $errors[] = 'اسم المجموعة بالعربية مطلوب';
        }

        // التحقق من عدم تكرار الاسم
        if (!empty($data['name_ar'])) {
            $company_id = current_user()['current_company_id'];
            $existing_group = $this->supplierGroupModel->findByNameAndCompany($data['name_ar'], $company_id);

            if ($existing_group && (!$excludeGroupNumber || $existing_group['group_number'] != $excludeGroupNumber)) {
                $errors[] = 'اسم المجموعة موجود بالفعل في الشركة الحالية';
            }
        }

        if (!empty($errors)) {
            throw new Exception(implode('<br>', $errors));
        }

        // إعداد البيانات
        return [
            'name_ar' => trim($data['name_ar']),
            'name_en' => trim($data['name_en'] ?? ''),
            'is_default' => isset($data['is_default']) && $data['is_default'] == '1' ? 1 : 0
        ];
    }
}
