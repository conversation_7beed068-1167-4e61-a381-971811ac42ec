<?php
namespace App\Modules\Sales\Models;

use PDO;
use Exception;

/**
 * Customer Model - نموذج العملاء
 */
class Customer
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table names
     */
    protected $table = 'concerned_entities';
    protected $detailsTable = 'customer_details';

    /**
     * Section ID for customers
     */
    protected $section_id;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;

        // الحصول على section_id للعملاء
        $stmt = $this->db->prepare("SELECT id FROM sections WHERE code = 'customers'");
        $stmt->execute();
        $this->section_id = $stmt->fetchColumn();
    }

    /**
     * الحصول على جميع العملاء للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT s.*, cd.*, g.name_ar as group_name
                FROM {$this->table} s
                LEFT JOIN {$this->detailsTable} cd ON s.id = cd.entity_id
                LEFT JOIN entity_groups g ON s.group_id = g.group_number
                    AND g.company_id = s.company_id AND g.section_id = s.section_id
                WHERE s.company_id = ? AND s.section_id = ?";
        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (isset($filters['status'])) {
            $sql .= " AND s.G_status = ?";
            $params[] = $filters['status'];
        }

        if (!empty($filters['group_id'])) {
            $sql .= " AND s.group_id = ?";
            $params[] = $filters['group_id'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (s.G_name_ar LIKE ? OR s.G_name_en LIKE ? OR cd.email LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
            $params[] = $search;
        }

        // ترتيب النتائج
        if (isset($filters['order_by'])) {
            $sql .= " ORDER BY " . $filters['order_by'];
        } else {
            $sql .= " ORDER BY created_at DESC";
        }

        // إضافة pagination - استخدام القيم مباشرة في MySQL
        if (isset($filters['limit']) && isset($filters['offset'])) {
            $limit = (int)$filters['limit'];
            $offset = (int)$filters['offset'];
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        } elseif (isset($filters['limit'])) {
            $limit = (int)$filters['limit'];
            $sql .= " LIMIT {$limit}";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

   /**
 * الحصول على عميل بالرقم الداخلي
 */
public function getByNumber($entity_number, $company_id)
{
    $sql = "SELECT 
                s.id AS entity_id,
                s.entity_number,
                s.G_name_ar,
                s.G_name_en,
                s.G_status,
                s.group_id,
                cd.email,
                cd.tax_number,
                cd.commercial_register,
                cd.customer_type,
                cd.credit_limit,
                cd.payment_terms,
                cd.discount_rate,
                cd.price_list,
                cd.rating,
                cd.sales_rep_id,
                cd.territory,
                cd.industry,
                cd.source,
                g.name_ar as group_name
            FROM {$this->table} s
            LEFT JOIN {$this->detailsTable} cd 
                ON s.id = cd.entity_id
            LEFT JOIN entity_groups g 
                ON s.group_id = g.group_number 
                AND g.company_id = s.company_id 
                AND g.section_id = s.section_id
            WHERE s.entity_number = :entity_number 
                AND s.company_id = :company_id 
                AND s.section_id = :section_id";

    $stmt = $this->db->prepare($sql);
    $stmt->execute([
        ':entity_number' => $entity_number,
        ':company_id' => $company_id,
        ':section_id' => $this->section_id
    ]);

    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * البحث عن عميل بالاسم والشركة
 */
public function findByNameAndCompany($name, $company_id)
{
    $sql = "SELECT * FROM {$this->table}
            WHERE G_name_ar = ? AND company_id = ? AND section_id = ?";
    $stmt = $this->db->prepare($sql);
    $stmt->execute([$name, $company_id, $this->section_id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * إنشاء عميل جديد
 */
public function create($data)
{
    // الحصول على الرقم التالي
    $nextNumber = $this->getNextNumber($data['company_id']);

    // إدراج البيانات الأساسية في concerned_entities
    $sql = "INSERT INTO {$this->table} (
                company_id, section_id, entity_number, group_id,
                G_name_ar, G_name_en, G_phone, G_mobile, G_website, G_notes, G_status,
                created_by, created_at
            ) VALUES (
                ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW()
            )";

    $stmt = $this->db->prepare($sql);
    $result = $stmt->execute([
        $data['company_id'],
        $this->section_id,
        $nextNumber,
        $data['group_id'] ?: null,
        $data['G_name_ar'],
        $data['G_name_en'] ?: null,
        $data['G_phone'] ?: null,
        $data['G_mobile'] ?: null,
        $data['G_website'] ?: null,
        $data['G_notes'] ?: null,
        $data['G_status'] ?: 'active',
        $data['created_by']
    ]);

    if (!$result) {
        throw new Exception('فشل في إدراج البيانات الأساسية');
    }

    // الحصول على ID الكيان الجديد من الجدول الأساسي
    $main_entity_id = $this->db->lastInsertId();

    // إدراج التفاصيل في customer_details
    $detailsSql = "INSERT INTO {$this->detailsTable} (
                    entity_id, company_id, email, tax_number, commercial_register,
                    customer_type, credit_limit, payment_terms, discount_rate,
                    price_list, rating, sales_rep_id, territory, industry, source
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
                )";

    $detailsStmt = $this->db->prepare($detailsSql);
    $detailsResult = $detailsStmt->execute([
        $main_entity_id, // ✅ استخدام ID الجدول الأساسي
        $data['company_id'],
        $data['email'] ?: null,
        $data['tax_number'] ?: null,
        $data['commercial_register'] ?: null,
        $data['customer_type'] ?: 'individual',
        $data['credit_limit'] ?: 0,
        $data['payment_terms'] ?: 0,
        $data['discount_rate'] ?: 0,
        $data['price_list'] ?: null,
        $data['rating'] ?: 'C',
        $data['sales_rep_id'] ?: null,
        $data['territory'] ?: null,
        $data['industry'] ?: null,
        $data['source'] ?: null
    ]);

    if (!$detailsResult) {
        throw new Exception('فشل في إدراج تفاصيل العميل');
    }

    // إرجاع array يحتوي على رقم الكيان و ID الأساسي
    return [
        'entity_number' => $nextNumber,
        'main_entity_id' => $main_entity_id
    ];
}


 /**
 * تحديث عميل
 */
public function update($entity_number, $data, $company_id)
{
    // الحصول على ID الكيان
    $entity = $this->getByNumber($entity_number, $company_id);
    if (!$entity) {
        throw new Exception('العميل غير موجود');
    }

    $entity_id = $entity['entity_id'];

    // تحديث البيانات الأساسية في concerned_entities
    $sql = "UPDATE {$this->table} SET
                group_id = ?, G_name_ar = ?, G_name_en = ?, G_phone = ?, G_mobile = ?,
                G_website = ?, G_notes = ?, G_status = ?, updated_by = ?, updated_at = NOW()
            WHERE entity_number = ? AND company_id = ? AND section_id = ?";

    $stmt = $this->db->prepare($sql);
    $result = $stmt->execute([
        $data['group_id'] ?: null,
        $data['G_name_ar'],
        $data['G_name_en'] ?: null,
        $data['G_phone'] ?: null,
        $data['G_mobile'] ?: null,
        $data['G_website'] ?: null,
        $data['G_notes'] ?: null,
        $data['G_status'] ?: 'active',
        $data['updated_by'],
        $entity_number,
        $company_id,
        $this->section_id
    ]);

    if (!$result) {
        throw new Exception('فشل في تحديث البيانات الأساسية');
    }

    // التحقق من وجود سجل في جدول التفاصيل
    $checkDetailsSql = "SELECT COUNT(*) FROM {$this->detailsTable} WHERE entity_id = ? AND company_id = ?";
    $checkStmt = $this->db->prepare($checkDetailsSql);
    $checkStmt->execute([$entity_id, $company_id]);
    $detailsExists = $checkStmt->fetchColumn() > 0;

    if ($detailsExists) {
        // تحديث التفاصيل إذا كان السجل موجودًا
        $detailsSql = "UPDATE {$this->detailsTable} SET
            email = ?, tax_number = ?, commercial_register = ?, customer_type = ?,
            credit_limit = ?, payment_terms = ?, discount_rate = ?, price_list = ?,
            rating = ?, sales_rep_id = ?, territory = ?, industry = ?, source = ?
        WHERE entity_id = ? AND company_id = ?";

        $detailsStmt = $this->db->prepare($detailsSql);
        $detailsResult = $detailsStmt->execute([
            $data['email'] ?: null,
            $data['tax_number'] ?: null,
            $data['commercial_register'] ?: null,
            $data['customer_type'] ?: 'individual',
            $data['credit_limit'] ?: 0,
            $data['payment_terms'] ?: 0,
            $data['discount_rate'] ?: 0,
            $data['price_list'] ?: null,
            $data['rating'] ?: 'C',
            $data['sales_rep_id'] ?: null,
            $data['territory'] ?: null,
            $data['industry'] ?: null,
            $data['source'] ?: null,
            $entity_id,
            $company_id
        ]);
    } else {
        // إنشاء سجل جديد إذا لم يكن موجودًا
        $detailsSql = "INSERT INTO {$this->detailsTable} (
            entity_id, company_id, email, tax_number, commercial_register, customer_type,
            credit_limit, payment_terms, discount_rate, price_list, rating,
            sales_rep_id, territory, industry, source
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?
        )";

        $detailsStmt = $this->db->prepare($detailsSql);
        $detailsResult = $detailsStmt->execute([
            $entity_id,
            $company_id,
            $data['email'] ?: null,
            $data['tax_number'] ?: null,
            $data['commercial_register'] ?: null,
            $data['customer_type'] ?: 'individual',
            $data['credit_limit'] ?: 0,
            $data['payment_terms'] ?: 0,
            $data['discount_rate'] ?: 0,
            $data['price_list'] ?: null,
            $data['rating'] ?: 'C',
            $data['sales_rep_id'] ?: null,
            $data['territory'] ?: null,
            $data['industry'] ?: null,
            $data['source'] ?: null
        ]);
    }

    if (!$detailsResult) {
        throw new Exception('فشل في تحديث أو إنشاء تفاصيل العميل');
    }

    return true;
}


    /**
     * حذف عميل
     */
    public function delete($entity_number, $company_id)
    {
        // الحصول على ID الكيان
        $entity = $this->getByNumber($entity_number, $company_id);
        if (!$entity) {
            throw new Exception('العميل غير موجود');
        }

        $entity_id = $entity['entity_id'];

        // التحقق من عدم وجود معاملات مع هذا العميل
        // هذا سيتم تطبيقه لاحقاً عند إنشاء جداول المعاملات

        // حذف العناوين والحسابات البنكية أولاً
        $addresses_deleted = $this->deleteAddresses($entity_id, $company_id);
        $bank_accounts_deleted = $this->deleteBankAccounts($entity_id, $company_id);

        // حذف التفاصيل من customer_details
        $detailsSql = "DELETE FROM customer_details WHERE entity_id = ?";
        $detailsStmt = $this->db->prepare($detailsSql);
        $details_deleted = $detailsStmt->execute([$entity_id]);

        // حذف الكيان الأساسي
        $sql = "DELETE FROM {$this->table}
                WHERE entity_number = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([$entity_number, $company_id, $this->section_id]);

        if (!$result) {
            throw new Exception('فشل في حذف العميل');
        }

        return true;
    }

    /**
     * الحصول على الرقم التالي للعميل
     */
 private function getNextNumber($company_id)
{
    // استعلام لإحضار آخر كود يحتوي على البادئة CUS (العملاء)
    $sql = "SELECT entity_number FROM {$this->table} 
            WHERE company_id = ? AND section_id = ?
              AND entity_number LIKE 'CUS%' 
            ORDER BY entity_number DESC 
            LIMIT 1";

    $stmt = $this->db->prepare($sql);
    $stmt->execute([$company_id, $this->section_id]);
    $lastCode = $stmt->fetchColumn();

    if ($lastCode) {
        // استخراج الجزء الرقمي من الكود بعد البادئة (3 أحرف)
        $numberPart = (int)substr($lastCode, 3);
    } else {
        $numberPart = 0;
    }

    $nextNumber = $numberPart + 1;

    // توليد الكود الجديد مع البادئة CUS
    $newCode = 'CUS' . str_pad($nextNumber, 5, '0', STR_PAD_LEFT);

    return $newCode;
}


    /**
     * الحصول على عدد العملاء مع الفلاتر
     */
    public function getCountByCompany($company_id, $filters = [])
    {
        $sql = "SELECT COUNT(*) FROM {$this->table} s
                LEFT JOIN entity_groups g ON s.group_id = g.group_number
                    AND g.company_id = s.company_id AND g.section_id = s.section_id
                WHERE s.company_id = ? AND s.section_id = ?";

        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (isset($filters['status']) && $filters['status'] !== '') {
            $sql .= " AND s.G_status = ?";
            $params[] = $filters['status'];
        }

        if (isset($filters['group_id']) && $filters['group_id'] !== '') {
            $sql .= " AND s.group_id = ?";
            $params[] = $filters['group_id'];
        }

        if (isset($filters['search']) && $filters['search'] !== '') {
            $sql .= " AND (s.G_name_ar LIKE ? OR s.G_name_en LIKE ? OR cd.email LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return (int)$stmt->fetchColumn();
    }

    /**
     * الحصول على إحصائيات العملاء
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي العملاء
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['total_customers'] = $stmt->fetchColumn();

        // العملاء النشطين
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND G_status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['active_customers'] = $stmt->fetchColumn();

        return $stats;
    }

    /**
     * الحصول على العملاء للاختيار
     */
    public function getForSelect($company_id)
    {
        $sql = "SELECT entity_number, G_name_ar FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND G_status = 'active'
                ORDER BY G_name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * بدء معاملة قاعدة البيانات
     */
    public function beginTransaction()
    {
        return $this->db->beginTransaction();
    }

    /**
     * تأكيد معاملة قاعدة البيانات
     */
    public function commit()
    {
        return $this->db->commit();
    }

    /**
     * إلغاء معاملة قاعدة البيانات
     */
    public function rollback()
    {
        return $this->db->rollback();
    }

    /**
     * الحصول على عناوين العميل
     */
    public function getAddresses($entity_id, $company_id)
    {
        $sql = "SELECT * FROM entity_addresses
                WHERE entity_id = ? AND company_id = ?
                ORDER BY is_default DESC, id ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$entity_id, $company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على الحسابات البنكية للعميل
     */
    public function getBankAccounts($entity_id, $company_id)
    {
        $sql = "SELECT * FROM entity_bank_accounts
                WHERE entity_id = ? AND company_id = ?
                ORDER BY is_default DESC, id ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$entity_id, $company_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على عميل مع العناوين والحسابات البنكية
     */
    public function getWithRelations($entity_number, $company_id)
    {
        // جلب بيانات العميل الأساسية
        $customer = $this->getByNumber($entity_number, $company_id);

        if ($customer) {
            // جلب العناوين
            $customer['addresses'] = $this->getAddresses($customer['entity_id'], $company_id);

            // جلب الحسابات البنكية
            $customer['bank_accounts'] = $this->getBankAccounts($customer['entity_id'], $company_id);
        }

        return $customer;
    }

    /**
     * حذف عناوين العميل
     */
    public function deleteAddresses($entity_id, $company_id)
    {
        $sql = "DELETE FROM entity_addresses WHERE entity_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$entity_id, $company_id]);
    }

    /**
     * حذف الحسابات البنكية للعميل
     */
    public function deleteBankAccounts($entity_id, $company_id)
    {
        $sql = "DELETE FROM entity_bank_accounts WHERE entity_id = ? AND company_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$entity_id, $company_id]);
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getDb()
    {
        return $this->db;
    }
}
