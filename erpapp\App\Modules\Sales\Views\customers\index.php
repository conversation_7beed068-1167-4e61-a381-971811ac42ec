<?php
/**
 * صفحة العملاء - مخصصة للجداول فقط
 * تتبع النظام الجديد باستخدام datatable_helper.php
 */

// إعداد الأعمدة
$columns = [
    [
        'field' => 'entity_number',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '120px',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'G_name_ar',
        'title' => 'اسم العميل',
        'type' => 'link',
        'url' => 'sales/customers/{entity_number}',
        'subtitle_field' => 'G_name_en',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'C_customer_type',
        'title' => 'نوع العميل',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text',
        'status_config' => [
            'classes' => [
                'individual' => 'primary',
                'company' => 'success'
            ],
            'texts' => [
                'individual' => 'فردي',
                'company' => 'شركة'
            ]
        ]
    ],
    [
        'field' => 'C_email',
        'title' => 'البريد الإلكتروني',
        'type' => 'email',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'group_name',
        'title' => 'المجموعة',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'G_phone',
        'title' => 'الهاتف',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'C_credit_limit',
        'title' => 'الحد الائتماني',
        'type' => 'currency',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'C_rating',
        'title' => 'التقييم',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text',
        'status_config' => [
            'classes' => [
                'A' => 'success',
                'B' => 'info',
                'C' => 'warning',
                'D' => 'danger'
            ],
            'texts' => [
                'A' => 'ممتاز',
                'B' => 'جيد',
                'C' => 'متوسط',
                'D' => 'ضعيف'
            ]
        ]
    ],
    [
        'field' => 'G_status',
        'title' => 'الحالة',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'secondary',
                'suspended' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق'
            ]
        ]
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '125px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'sales/customers/{entity_number}',
                'class' => 'btn-primary',
                'icon' => 'fas fa-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'sales/customers/{entity_number}/edit',
                'class' => 'btn-success',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'class' => 'btn-danger',
                'icon' => 'fas fa-trash',
                'title' => 'حذف',
                'onclick' => 'confirmDelete({entity_number})'
            ]
        ]
    ]
];

// إعداد الإجراءات (بدون أزرار CSV)
$actions = [
    [
        'type' => 'success',
        'url' => 'sales/customers/dashboard',
        'icon' => 'fas fa-tachometer-alt',
        'text' => 'لوحة التحكم'
    ],
    [
        'type' => 'info',
        'url' => 'sales/customers/stats',
        'icon' => 'fas fa-chart-bar',
        'text' => 'عرض الإحصائيات'
    ],
    [
        'type' => 'primary',
        'url' => 'sales/customers/create',
        'icon' => 'fas fa-plus-circle',
        'text' => 'إضافة عميل جديد'
    ]
];

// إعداد أزرار CSV للجدول (بدون قالب الاستيراد)
$csv_actions = [
    [
        'type' => 'csv_export',
        'url' => 'sales/customers/export',
        'icon' => 'fas fa-download',
        'text' => 'تصدير CSV'
    ],
    [
        'type' => 'csv_import',
        'icon' => 'fas fa-upload',
        'text' => 'استيراد CSV'
    ]
];

// إعداد الفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في العملاء',
        'placeholder' => 'ابحث بالاسم أو البريد الإلكتروني...',
        'icon' => 'fas fa-search',
        'col_size' => 6,
        'help' => 'البحث في الاسم العربي، الإنجليزي، أو البريد الإلكتروني'
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'حالة العميل',
        'placeholder' => 'جميع الحالات',
        'icon' => 'fas fa-check-circle',
        'col_size' => 3,
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ]
    ],
    [
        'name' => 'group_id',
        'type' => 'select',
        'label' => 'مجموعة العملاء',
        'placeholder' => 'جميع المجموعات',
        'icon' => 'fas fa-folder',
        'col_size' => 3,
       'options' => array_column($customerGroups ?? [], 'name_ar', 'group_number')
    ]
];

// ملاحظة: الإحصائيات تم نقلها إلى statistics.php
// هذه الصفحة مخصصة للجدول فقط

// إعداد Empty State
$empty_state = [
    'icon' => 'fas fa-users',
    'message' => 'لا توجد عملاء',
    'action' => [
        'url' => 'sales/customers/create',
        'text' => 'إضافة عميل جديد'
    ]
];

// إعداد breadcrumb آمن
$safe_breadcrumb = [
    ['title' => 'المبيعات', 'url' => 'sales'],
    ['title' => 'العملاء', 'active' => true]
];

// استخدام النظام الجديد - مخصص للجداول فقط
render_datatable_page([
    'title' => $title ?? 'العملاء',
    'module' => 'sales',
    'entity' => 'customers',
    'data' => $customers ?? [],
    'columns' => $columns,
    'pagination' => $pagination ?? [],
    'filters' => $filters ?? [],
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'csv_actions' => $csv_actions,
    'filters_config' => $filters_config,
    'empty_state' => $empty_state
]);
?>