<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات
$actions = [
    [
        'type' => 'secondary',
        'url' => 'sales/customer-groups',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'info',
        'url' => 'sales/customer-groups/' . $customerGroup['group_number'],
        'icon' => 'fas fa-eye',
        'text' => 'عرض المجموعة'
    ],
    [
        'type' => 'primary',
        'form' => 'customerGroupForm',
        'icon' => 'fas fa-save',
        'text' => 'حفظ التغييرات',
        'submit' => true
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المبيعات', 'url' => 'sales'],
    ['title' => 'مجموعات العملاء', 'url' => 'sales/customer-groups'],
    ['title' => 'تعديل مجموعة', 'active' => true]
];

// استخدام النظام الموحد
render_form_page([
    'title' => $title ?? 'تعديل مجموعة العملاء - ' . $customerGroup['name_ar'],
    'module' => 'sales',
    'entity' => 'customer-groups',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => base_url('sales/customer-groups/' . $customerGroup['group_number'] . '/update'),
        'method' => 'POST',
        'id' => 'customerGroupForm',
        'tabs' => $form_tabs,
        'data' => $customerGroup // تمرير البيانات الحالية للنموذج
    ]
]);
?>


