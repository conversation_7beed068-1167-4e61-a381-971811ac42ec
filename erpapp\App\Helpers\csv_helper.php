<?php
/**
 * CSV Helper - مساعد ملفات CSV
 * نظام ديناميكي للاستيراد والتصدير
 */

/**
 * تصدير البيانات إلى CSV
 */
function export_to_csv($data, $columns, $config = [])
{
    try {
        $title = $config['title'] ?? 'تصدير البيانات';
        $filename = $config['filename'] ?? 'export_' . date('Y-m-d_H-i-s');
        
        // إنشاء مجلد التصدير إذا لم يكن موجود
        $export_dir = BASE_PATH . '/storage/exports';
        if (!is_dir($export_dir)) {
            mkdir($export_dir, 0755, true);
        }
        
        $filepath = $export_dir . '/' . $filename . '.csv';
        $file = fopen($filepath, 'w');
        
        if (!$file) {
            throw new Exception('فشل في إنشاء ملف التصدير');
        }
        
        // إضافة BOM للدعم العربي
        fwrite($file, "\xEF\xBB\xBF");
        
        // إضافة رؤوس الأعمدة
        $headers = [];
        foreach ($columns as $column) {
            if (isset($column['export']) && $column['export'] === false) {
                continue;
            }
            $headers[] = $column['title'];
        }
        fputcsv($file, $headers);
        
        // إضافة البيانات
        $records_count = 0;
        foreach ($data as $row) {
            $csv_row = [];
            foreach ($columns as $column) {
                if (isset($column['export']) && $column['export'] === false) {
                    continue;
                }
                $field = $column['field'];
                $value = $row[$field] ?? '';
                
                // تنسيق القيم حسب النوع
                $value = format_csv_value($value, $column);
                $csv_row[] = $value;
            }
            fputcsv($file, $csv_row);
            $records_count++;
        }
        
        fclose($file);
        
        return [
            'success' => true,
            'filepath' => $filepath,
            'filename' => $filename . '.csv',
            'records_count' => $records_count,
            'download_url' => base_url('storage/exports/' . $filename . '.csv')
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * قراءة ملف CSV
 */
function read_csv_file($filepath)
{
    try {
        if (!file_exists($filepath)) {
            return [
                'success' => false,
                'error' => 'الملف غير موجود'
            ];
        }
        
        $data = [];
        $headers = [];
        $row_count = 0;
        
        // فتح الملف
        $handle = fopen($filepath, 'r');
        if (!$handle) {
            return [
                'success' => false,
                'error' => 'فشل في فتح الملف'
            ];
        }
        
        // تجربة قراءة السطر الأول لتحديد نوع الفاصل
        $first_line = fgets($handle);
        rewind($handle);

        // تحديد نوع الفاصل
        $delimiter = ',';
        if (substr_count($first_line, ';') > substr_count($first_line, ',')) {
            $delimiter = ';';
        }

        // قراءة الرؤوس (السطر الأول)
        $headers = fgetcsv($handle, 1000, $delimiter);
        if (!$headers) {
            fclose($handle);
            return [
                'success' => false,
                'error' => 'الملف فارغ أو تالف'
            ];
        }

        // تنظيف الرؤوس من BOM والمسافات
        $headers = array_map(function($header) {
            return trim(str_replace(["\xEF\xBB\xBF", "\r", "\n"], '', $header));
        }, $headers);

        // تسجيل للتشخيص
        error_log('CSV Delimiter: ' . $delimiter);
        error_log('CSV Headers: ' . print_r($headers, true));

        // قراءة البيانات
        $data = [];
        $row_count = 0;
        while (($row = fgetcsv($handle, 1000, $delimiter)) !== false) {
            // تجاهل الصفوف الفارغة
            if (count(array_filter($row)) === 0) {
                continue;
            }
            
            if (count($row) >= count($headers)) {
                $row_data = [];
                for ($i = 0; $i < count($headers); $i++) {
                    $row_data[$headers[$i]] = isset($row[$i]) ? trim($row[$i]) : '';
                }
                $data[] = $row_data;
                $row_count++;
            }
        }
        
        fclose($handle);
        
        return [
            'success' => true,
            'headers' => $headers,
            'data' => $data,
            'rows_count' => $row_count
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'خطأ في قراءة ملف CSV: ' . $e->getMessage()
        ];
    }
}

/**
 * إنشاء قالب CSV للاستيراد
 */
function create_csv_template($columns, $config = [])
{
    try {
        $title = $config['title'] ?? 'قالب الاستيراد';
        $filename = $config['filename'] ?? 'import_template_' . date('Y-m-d_H-i-s');
        
        // إنشاء مجلد القوالب إذا لم يكن موجود
        $templates_dir = BASE_PATH . '/storage/templates';
        if (!is_dir($templates_dir)) {
            mkdir($templates_dir, 0755, true);
        }
        
        $filepath = $templates_dir . '/' . $filename . '.csv';
        $file = fopen($filepath, 'w');
        
        if (!$file) {
            throw new Exception('فشل في إنشاء ملف القالب');
        }
        
        // إضافة BOM للدعم العربي
        fwrite($file, "\xEF\xBB\xBF");
        
        // إضافة رؤوس الأعمدة
        $headers = [];
        $examples = [];
        
        foreach ($columns as $column) {
            $title = $column['title'];
            if (isset($column['required']) && $column['required']) {
                $title .= ' *';
            }
            $headers[] = $title;
            
            // إضافة أمثلة
            $examples[] = get_column_example($column);
        }
        
        fputcsv($file, $headers);
        fputcsv($file, $examples);
        
        fclose($file);
        
        return [
            'success' => true,
            'filepath' => $filepath,
            'filename' => $filename . '.csv',
            'download_url' => base_url('storage/templates/' . $filename . '.csv')
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

/**
 * تنسيق قيمة للتصدير
 */
function format_csv_value($value, $column)
{
    if (empty($value)) {
        return '';
    }
    
    $type = $column['type'] ?? 'text';
    
    switch ($type) {
        case 'date':
            return date('Y-m-d', strtotime($value));
        case 'datetime':
            return date('Y-m-d H:i:s', strtotime($value));
        case 'number':
            return is_numeric($value) ? $value : '0';
        case 'badge':
            if (isset($column['status_config']['texts'][$value])) {
                return $column['status_config']['texts'][$value];
            }
            return $value;
        default:
            return $value;
    }
}

/**
 * الحصول على مثال للعمود
 */
function get_column_example($column)
{
    $field = $column['field'];
    $type = $column['data_type'] ?? 'text';
    
    // أمثلة حسب اسم الحقل
    $examples = [
        'G_name_ar' => 'شركة المثال المحدودة',
        'G_name_en' => 'Example Company Ltd',
        'G_phone' => '0112345678',
        'G_mobile' => '0501234567',
        'S_email' => '<EMAIL>',
        'G_website' => 'www.example.com',
        'S_tax_number' => '123456789012345',
        'S_commercial_register' => '1010123456',
        'G_status' => 'active',
        'name_ar' => 'مجموعة المثال',
        'name_en' => 'Example Group'
    ];
    
    if (isset($examples[$field])) {
        return $examples[$field];
    }
    
    // أمثلة حسب نوع البيانات
    switch ($type) {
        case 'email':
            return '<EMAIL>';
        case 'url':
            return 'www.example.com';
        case 'number':
            return '100';
        case 'date':
            return date('Y-m-d');
        case 'select':
            if (isset($column['import_options']) && is_array($column['import_options'])) {
                return array_keys($column['import_options'])[0];
            }
            return 'option1';
        default:
            return 'مثال';
    }
}

/**
 * تنزيل ملف
 */
function download_csv_file($filepath, $filename)
{
    if (!file_exists($filepath)) {
        throw new Exception('الملف غير موجود');
    }
    
    // تعيين headers للتنزيل
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($filepath));
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    
    // قراءة وإرسال الملف
    readfile($filepath);
    
    // حذف الملف المؤقت (اختياري)
    if (strpos($filepath, '/storage/exports/') !== false || strpos($filepath, '/storage/templates/') !== false) {
        // لا نحذف ملفات التصدير والقوالب
    } else {
        unlink($filepath);
    }
    
    exit;
}
?>
