<?php
namespace App\Modules\Sales\Controllers;

use App\Modules\Sales\Models\Customer;
use App\Modules\Sales\Models\CustomerGroup;
use Exception;
use PDO;

class CustomerController
{
    protected $params = [];
    protected $customerModel;
    protected $customerGroupModel;

    protected $module = 'sales';
    protected $entity = 'customers';
    protected $moduleTitle = 'المبيعات';
    protected $entityTitle = 'العملاء';
    protected $entityTitleSingular = 'العميل';

    public function __construct($params = [])
    {
        $this->params = $params;
      $this->customerModel = new Customer();
        $this->customerGroupModel = new CustomerGroup();

        if (!is_logged_in()) {
            redirect(base_url('login'));
        }

        $user = current_user();
        if (!$user || !$user['current_company_id']) {
            flash($this->entity . '_error', 'يجب تحديد شركة حالية للوصول إلى ' . $this->entityTitle, 'warning');
            redirect(base_url('companies'));
        }
    }

    public function index()
    {
        $data = handle_datatable_index([
            'filter_name' => $this->entity,
            'default_filters' => ['status' => '', 'group_id' => ''],
            'filter_fields' => ['search', 'status', 'group_id'],
            'model' => $this->customerModel,
            'title' => $this->entityTitle,
            'data_key' => $this->entity,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'active' => true]
            ],
            'additional_data' => [
                'customerGroups' => $this->customerGroupModel->getForSelect(current_user()['current_company_id'])
            ]
        ]);

        view(ucfirst($this->module) . "::" . $this->entity . "/index", $data);
    }

    public function applyFilters()
    {
        handle_apply_filters($this->entity, ['status', 'group_id'], base_url($this->module . '/' . $this->entity));
    }

    public function clearFilters()
    {
        handle_clear_filters($this->entity, base_url($this->module . '/' . $this->entity));
    }

    public function stats()
    {
        $company_id = current_user()['current_company_id'];

        $stats = $this->customerModel->getStats($company_id);

        $data = [
            'title' => 'إحصائيات ' . $this->entityTitle,
            'stats' => $stats,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'الإحصائيات', 'active' => true]
            ]
        ];

        view(ucfirst($this->module) . "::" . $this->entity . "/stats", $data);
    }

    public function mixed()
    {
        $data = handle_datatable_index([
            'filter_name' => $this->entity,
            'default_filters' => ['status' => '', 'group_id' => ''],
            'filter_fields' => ['search', 'status', 'group_id'],
            'model' => $this->customerModel,
            'title' => $this->entityTitle . ' - عرض مختلط',
            'data_key' => $this->entity,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle . ' - عرض مختلط', 'active' => true]
            ],
            'additional_data' => [
                'customerGroups' => $this->customerGroupModel->getForSelect(current_user()['current_company_id'])
            ]
        ]);

        $company_id = current_user()['current_company_id'];
        $data['stats'] = $this->customerModel->getStats($company_id);

        view(ucfirst($this->module) . "::" . $this->entity . "/mixed", $data);
    }

    public function create()
    {
        $company_id = current_user()['current_company_id'];

        $customerGroups = $this->customerGroupModel->getForSelect($company_id);
        $defaultGroup = $this->customerGroupModel->getDefaultGroup($company_id);

        $data = [
            'title' => 'إضافة ' . $this->entityTitleSingular . ' جديد',
            'customerGroups' => $customerGroups,
            'defaultGroup' => $defaultGroup,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
                ['title' => 'إضافة ' . $this->entityTitleSingular, 'active' => true]
            ]
        ];

        view(ucfirst($this->module) . "::" . $this->entity . "/create", $data);
    }

    public function store()
    {
        try {
            $company_id = current_user()['current_company_id'];
            $user_id = current_user()['UserID'];

            $this->customerModel->beginTransaction();

            $validatedData = $this->validateCustomerData($_POST);
            $validatedData['company_id'] = $company_id;
            $validatedData['created_by'] = $user_id;

            $result = $this->customerModel->create($validatedData);

            if ($result && isset($result['entity_number'])) {
                $customerNumber = $result['entity_number'];
                $main_entity_id = $result['main_entity_id']; // ✅ استخدام ID الصحيح

                if (!empty($_POST['addresses'])) {
                    $this->saveAddresses($main_entity_id, $company_id, $_POST['addresses'], $_POST['default_address'] ?? 0, $user_id);
                }

                if (!empty($_POST['bank_accounts'])) {
                    $this->saveBankAccounts($main_entity_id, $company_id, $_POST['bank_accounts'], $_POST['default_bank_account'] ?? 0, $user_id);
                }

                $this->customerModel->commit();

                flash('success', 'تم إنشاء ' . $this->entityTitleSingular . ' بنجاح مع جميع البيانات');
                redirect(base_url("{$this->module}/{$this->entity}"));
            } else {
                $this->customerModel->rollback();
                flash('error', 'حدث خطأ أثناء إنشاء ' . $this->entityTitleSingular);
                redirect(base_url("{$this->module}/{$this->entity}/create"));
            }
        } catch (Exception $e) {
            $this->customerModel->rollback();
            flash('error', $e->getMessage());
            redirect(base_url("{$this->module}/{$this->entity}/create"));
        }
    }

    public function show()
    {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $customer = $this->customerModel->getWithRelations($entity_number, $company_id);

        if (!$customer) {
            flash('error', $this->entityTitleSingular . ' غير موجود');
            redirect(base_url("{$this->module}/{$this->entity}"));
        }

        $customerGroups = $this->customerGroupModel->getForSelect($company_id);

        $data = [
            'title' => 'عرض ' . $this->entityTitleSingular . ' - ' . $customer['G_name_ar'],
            'customer' => $customer,
            'customerGroups' => $customerGroups,
            'breadcrumb' => [
                ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
                ['title' => $this->entityTitle, 'url' => base_url("{$this->module}/{$this->entity}")],
                ['title' => 'عرض ' . $this->entityTitleSingular, 'active' => true]
            ]
        ];

        view(ucfirst($this->module) . "::" . $this->entity . "/show", $data);
    }


/**
 * عرض صفحة تعديل عميل
 */
public function edit()
{
    $entity_number = $this->params['id'];
    $company_id = current_user()['current_company_id'];

    $customer = $this->customerModel->getWithRelations($entity_number, $company_id);

    if (!$customer) {
        flash('error', $this->entityTitleSingular . ' غير موجود');
        redirect(base_url($this->module . '/' . $this->entity));
    }

    // الحصول على مجموعات العملاء
    $customerGroups = $this->customerGroupModel->getForSelect($company_id);

    $data = [
        'title' => 'تعديل ' . $this->entityTitleSingular . ' - ' . $customer['G_name_ar'],
        'customer' => $customer,
        'customerGroups' => $customerGroups,
        'breadcrumb' => [
            ['title' => $this->moduleTitle, 'url' => base_url($this->module)],
            ['title' => $this->entityTitle, 'url' => base_url($this->module . '/' . $this->entity)],
            ['title' => 'تعديل ' . $this->entityTitleSingular, 'active' => true]
        ]
    ];

    view(ucfirst($this->module) . "::" . $this->entity . "/edit", $data);
}


/**
 * تحديث عميل
 */
public function update()
{
    try {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        // التحقق من وجود العميل
        $customer = $this->customerModel->getByNumber($entity_number, $company_id);
        if (!$customer) {
            flash('error', $this->entityTitleSingular . ' غير موجود');
            redirect(base_url($this->module . '/' . $this->entity));
        }

        // التحقق من صحة البيانات
        $validatedData = $this->validateCustomerData($_POST, $entity_number);

        // فحص التغييرات
        if (!$this->hasDataChanged($customer, $validatedData, $_POST)) {
            flash('info', 'لا توجد تغييرات لحفظها');
            redirect(base_url($this->module . '/' . $this->entity . '/' . $entity_number . '/edit'));
            return;
        }

        $validatedData['updated_by'] = current_user()['UserID'];

        $db = $this->customerModel->getDb();
        $db->beginTransaction();

        try {
            $result = $this->customerModel->update($entity_number, $validatedData, $company_id);
            if (!$result) {
                throw new Exception('فشل في تحديث البيانات الأساسية لـ' . $this->entityTitleSingular);
            }

            $entity_id = $customer['id'];
            $user_id = current_user()['UserID'];

            $this->updateAddresses($entity_id, $company_id, $_POST, $user_id);
            $this->updateBankAccounts($entity_id, $company_id, $_POST, $user_id);

            $db->commit();

            flash('success', 'تم تحديث ' . $this->entityTitleSingular . ' بنجاح - رقم: ' . $entity_number);
            redirect(base_url($this->module . '/' . $this->entity));

        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }

    } catch (Exception $e) {
        flash('error', $e->getMessage());
        redirect(base_url($this->module . '/' . $this->entity . '/' . $this->params['id'] . '/edit'));
    }
}


/**
 * حذف عميل
 */
public function delete()
{
    try {
        $entity_number = $this->params['id'];
        $company_id = current_user()['current_company_id'];

        $customer = $this->customerModel->getByNumber($entity_number, $company_id);
        if (!$customer) {
            flash('error', $this->entityTitleSingular . ' غير موجود');
            redirect(base_url($this->module . '/' . $this->entity));
        }

        // بدء المعاملة
        $this->customerModel->beginTransaction();

        try {
            // استخدام دالة الحذف من النموذج (تحذف جميع البيانات المرتبطة)
            $result = $this->customerModel->delete($entity_number, $company_id);

            if ($result) {
                $this->customerModel->commit();
                flash('success', 'تم حذف ' . $this->entityTitleSingular . ' بنجاح مع جميع البيانات المرتبطة');
            } else {
                $this->customerModel->rollback();
                flash('error', 'حدث خطأ أثناء حذف ' . $this->entityTitleSingular);
            }

        } catch (Exception $e) {
            $this->customerModel->rollback();
            throw $e;
        }

    } catch (Exception $e) {
        flash('error', $e->getMessage());
    }

    redirect(base_url($this->module . '/' . $this->entity));
}

   /**
 * فحص ما إذا كانت البيانات تغيرت
 */
private function hasDataChanged($originalData, $newData, $postData = [])
{
    // الحقول التي نريد مقارنتها
    $fieldsToCompare = [
        'group_id', 'G_name_ar', 'G_name_en', 'G_phone', 'G_mobile',
        'G_website', 'G_notes', 'G_status', 'email', 'tax_number',
        'commercial_register', 'credit_limit', 'payment_terms',
        'discount_rate', 'customer_type', 'price_list', 'sales_rep_id',
        'territory', 'industry', 'source', 'rating'
    ];

    foreach ($fieldsToCompare as $field) {
        $originalValue = $originalData[$field] ?? '';
        $newValue = $newData[$field] ?? '';

        // تنظيف القيم للمقارنة
        $cleanOriginal = trim((string)$originalValue);
        $cleanNew = trim((string)$newValue);

        // معالجة القيم الفارغة والـ null
        if ($cleanOriginal === '' && $cleanNew === '') continue;
        if ($cleanOriginal === '' && $cleanNew === '0') continue;
        if ($cleanOriginal === '0' && $cleanNew === '') continue;

        // مقارنة القيم
        if ($cleanOriginal !== $cleanNew) {
            return true; // يوجد تغيير
        }
    }

    // فحص تغييرات العناوين
    if (!empty($postData) && $this->hasAddressesChanged($originalData, $postData)) {
        return true;
    }

    // فحص تغييرات الحسابات البنكية
    if (!empty($postData) && $this->hasBankAccountsChanged($originalData, $postData)) {
        return true;
    }

    return false; // لا توجد تغييرات
}

/**
 * فحص تغييرات العناوين
 */
private function hasAddressesChanged($originalData, $postData)
{
    $entity_id = $originalData['id'] ?? 0;
    $currentAddresses = $this->getCurrentAddresses($entity_id);
    $newAddresses = $postData['addresses'] ?? [];

    if (count($currentAddresses) !== count($newAddresses)) {
        return true;
    }

    // يمكن لاحقًا مقارنة تفاصيل كل عنوان هنا
    return false;
}

/**
 * فحص تغييرات الحسابات البنكية
 */
private function hasBankAccountsChanged($originalData, $postData)
{
    $entity_id = $originalData['id'] ?? 0;
    $currentBankAccounts = $this->getCurrentBankAccounts($entity_id);
    $newBankAccounts = $postData['bank_accounts'] ?? [];

    if (count($currentBankAccounts) !== count($newBankAccounts)) {
        return true;
    }

    // يمكن لاحقًا مقارنة تفاصيل كل حساب بنكي هنا
    return false;
}


/**
 * الحصول على العناوين الحالية
 */
private function getCurrentAddresses($entityId)
{
    if (!$entityId) return [];

    $sql = "SELECT address_type, address_label, address_line1, city, state_province,
                   postal_code, country, phone, is_default
            FROM entity_addresses
            WHERE entity_id = ?
            ORDER BY id";

    $db = $this->customerModel->getDb();
    $stmt = $db->prepare($sql);
    $stmt->execute([$entityId]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * الحصول على الحسابات البنكية الحالية
 */
private function getCurrentBankAccounts($entityId)
{
    if (!$entityId) return [];

    $sql = "SELECT bank_name, account_number, account_name, iban, currency, is_default
            FROM entity_bank_accounts
            WHERE entity_id = ?
            ORDER BY id";

    $db = $this->customerModel->getDb();
    $stmt = $db->prepare($sql);
    $stmt->execute([$entityId]);

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * التحقق من صحة بيانات العميل
 */
private function validateCustomerData($data, $excludeEntityNumber = null)
{
    $errors = [];

    if (empty($data['G_name_ar'])) {
        $errors[] = 'اسم العميل بالعربية مطلوب';
    }

    if (!empty($data['G_name_ar'])) {
        $company_id = current_user()['current_company_id'];
        $existing_customer = $this->customerModel->findByNameAndCompany($data['G_name_ar'], $company_id);

        if ($existing_customer && (!$excludeEntityNumber || $existing_customer['entity_number'] != $excludeEntityNumber)) {
            $errors[] = 'اسم العميل موجود بالفعل في الشركة الحالية';
        }
    }

    if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }

    if (!empty($data['G_website']) && !filter_var($data['G_website'], FILTER_VALIDATE_URL)) {
        $errors[] = 'الموقع الإلكتروني غير صحيح';
    }

    if (!empty($errors)) {
        throw new Exception(implode('<br>', $errors));
    }

    return [
        'group_id' => !empty($data['group_id']) ? (int)$data['group_id'] : null,
        'G_name_ar' => trim($data['G_name_ar']),
        'G_name_en' => trim($data['G_name_en'] ?? ''),
        'G_phone' => trim($data['G_phone'] ?? ''),
        'G_mobile' => trim($data['G_mobile'] ?? ''),
        'G_website' => trim($data['G_website'] ?? ''),
        'G_notes' => trim($data['G_notes'] ?? ''),
        'G_status' => $data['G_status'] ?? 'active',
        'email' => trim($data['email'] ?? ''),
        'tax_number' => trim($data['tax_number'] ?? ''),
        'commercial_register' => trim($data['commercial_register'] ?? ''),
        'credit_limit' => !empty($data['credit_limit']) ? (float)$data['credit_limit'] : 0,
        'payment_terms' => !empty($data['payment_terms']) ? (int)$data['payment_terms'] : 0,
        'discount_rate' => !empty($data['discount_rate']) ? (float)$data['discount_rate'] : 0,
        'customer_type' => $data['customer_type'] ?? 'individual',
        'price_list' => !empty($data['price_list']) ? (int)$data['price_list'] : null,
        'sales_rep_id' => !empty($data['sales_rep_id']) ? (int)$data['sales_rep_id'] : null,
        'territory' => trim($data['territory'] ?? ''),
        'industry' => trim($data['industry'] ?? ''),
        'source' => trim($data['source'] ?? ''),
        'rating' => $data['rating'] ?? 'C'
    ];
}

/**
 * تحديث عناوين المورد
 */
private function updateAddresses($entity_id, $company_id, $postData, $user_id)
{
    $db = $this->customerModel->getDb();

    // حذف العناوين القديمة
    $deleteSql = "DELETE FROM entity_addresses WHERE entity_id = ? AND company_id = ?";
    $deleteStmt = $db->prepare($deleteSql);
    $deleteStmt->execute([$entity_id, $company_id]);

    // إضافة العناوين الجديدة إذا وجدت
    if (!empty($postData['addresses'])) {
        $default_index = $postData['default_address'] ?? 0;
        $this->saveAddresses($entity_id, $company_id, $postData['addresses'], $default_index, $user_id);
    }
}

/**
 * تحديث الحسابات البنكية للمورد
 */
private function updateBankAccounts($entity_id, $company_id, $postData, $user_id)
{
    $db = $this->customerModel->getDb();

    // حذف الحسابات البنكية القديمة
    $deleteSql = "DELETE FROM entity_bank_accounts WHERE entity_id = ? AND company_id = ?";
    $deleteStmt = $db->prepare($deleteSql);
    $deleteStmt->execute([$entity_id, $company_id]);

    // إضافة الحسابات الجديدة إذا وجدت
    if (!empty($postData['bank_accounts'])) {
        $default_index = $postData['default_bank_account'] ?? 0;
        $this->saveBankAccounts($entity_id, $company_id, $postData['bank_accounts'], $default_index, $user_id);
    }
}

/**
 * حفظ عناوين المورد
 */
private function saveAddresses($entity_id, $company_id, $addresses, $default_index, $user_id)
{
    $sql = "INSERT INTO entity_addresses (
                entity_id, company_id, address_type, address_label, address_line1,
                city, state_province, postal_code, country, phone, is_default,
                created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

    $db = $this->customerModel->getDb();
    $stmt = $db->prepare($sql);

    foreach ($addresses as $index => $address) {
        if (empty($address['address_type']) || empty($address['address_label']) || empty($address['address_line1'])) {
            continue; // تخطي العناوين غير المكتملة
        }

        $is_default = ($index == $default_index) ? 1 : 0;

        $stmt->execute([
            $entity_id,
            $company_id,
            $address['address_type'],
            $address['address_label'],
            $address['address_line1'],
            $address['city'],
            $address['state_province'] ?? null,
            $address['postal_code'] ?? null,
            $address['country'] ?? 'Saudi Arabia',
            $address['phone'] ?? null,
            $is_default,
            $user_id
        ]);
    }
}

/**
 * حفظ الحسابات البنكية للمورد
 */
private function saveBankAccounts($entity_id, $company_id, $bank_accounts, $default_index, $user_id)
{
    $sql = "INSERT INTO entity_bank_accounts (
                entity_id, company_id, bank_name, account_number, account_name,
                iban, currency, is_default, created_by, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";

    $db = $this->customerModel->getDb();
    $stmt = $db->prepare($sql);

    foreach ($bank_accounts as $index => $account) {
        if (empty($account['bank_name']) || empty($account['account_number']) || empty($account['account_name'])) {
            continue; // تخطي الحسابات غير المكتملة
        }

        $is_default = ($index == $default_index) ? 1 : 0;

        $stmt->execute([
            $entity_id,
            $company_id,
            $account['bank_name'],
            $account['account_number'],
            $account['account_name'],
            $account['iban'] ?? null,
            $account['currency'] ?? 'SAR',
            $is_default,
            $user_id
        ]);
    }
}



}
