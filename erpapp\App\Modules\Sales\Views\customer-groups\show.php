<?php
// تضمين القالب المشترك للحصول على $form_tabs
include __DIR__ . '/form_template.php';

// إعداد الإجراءات للعرض فقط
$actions = [
    [
        'type' => 'secondary',
        'url' => 'sales/customer-groups',
        'icon' => 'fas fa-arrow-left',
        'text' => 'العودة للقائمة'
    ],
    [
        'type' => 'primary',
        'url' => 'sales/customer-groups/' . ($customerGroup['group_number'] ?? '') . '/edit',
        'icon' => 'fas fa-edit',
        'text' => 'تعديل المجموعة'
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المبيعات', 'url' => 'sales'],
    ['title' => 'مجموعات العملاء', 'url' => 'sales/customer-groups'],
    ['title' => $customerGroup['name_ar'] ?? 'عرض المجموعة', 'active' => true]
];

// استخدام النظام الموحد في وضع القراءة فقط
render_form_page([
    'title' => $title ?? 'عرض مجموعة العملاء - ' . ($customerGroup['name_ar'] ?? ''),
    'module' => 'sales',
    'entity' => 'customer-groups',
    'breadcrumb' => $safe_breadcrumb,
    'actions' => $actions,
    'form_data' => $customerGroup ?? [], // بيانات المجموعة الحالية
    'readonly' => true, // وضع القراءة فقط
    // النموذج بالتبويبات الديناميكية
    'form' => [
        'action' => '#', // لا يوجد action في وضع العرض
        'method' => 'GET',
        'id' => 'customerGroupForm',
        'tabs' => $form_tabs
    ]
]);
?>
