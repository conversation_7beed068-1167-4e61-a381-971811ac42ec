<?php
namespace App\Modules\Sales;

use App\Core\Module as BaseModule;

/**
 * وحدة المبيعات
 */
class Module extends BaseModule
{
    /**
     * تسجيل المسارات الخاصة بالوحدة
     */
    public function registerRoutes()
    {
        // مسارات لوحة تحكم المبيعات
        add_route('GET', '/sales', 'App\Modules\Sales\Controllers\SaleController@index');

        // مسارات مجموعات العملاء
        add_route('GET', '/sales/customer-groups', 'App\Modules\Sales\Controllers\CustomerGroupController@index');
        add_route('POST', '/sales/customer-groups/apply-filters', 'App\Modules\Sales\Controllers\CustomerGroupController@applyFilters');
        add_route('GET', '/sales/customer-groups/clear-filters', 'App\Modules\Sales\Controllers\CustomerGroupController@clearFilters');
        add_route('GET', '/sales/customer-groups/create', 'App\Modules\Sales\Controllers\CustomerGroupController@create');
        add_route('POST', '/sales/customer-groups/store', 'App\Modules\Sales\Controllers\CustomerGroupController@store');
        add_route('GET', '/sales/customer-groups/{id}', 'App\Modules\Sales\Controllers\CustomerGroupController@show');
        add_route('GET', '/sales/customer-groups/{id}/edit', 'App\Modules\Sales\Controllers\CustomerGroupController@edit');
        add_route('POST', '/sales/customer-groups/{id}/update', 'App\Modules\Sales\Controllers\CustomerGroupController@update');
        add_route('POST', '/sales/customer-groups/{id}/delete', 'App\Modules\Sales\Controllers\CustomerGroupController@delete');

        // مسارات العملاء - الروابط الثابتة أولاً
        add_route('GET', '/sales/customers', 'App\Modules\Sales\Controllers\CustomerController@index');
        add_route('POST', '/sales/customers/apply-filters', 'App\Modules\Sales\Controllers\CustomerController@applyFilters');
        add_route('GET', '/sales/customers/clear-filters', 'App\Modules\Sales\Controllers\CustomerController@clearFilters');
        add_route('GET', '/sales/customers/stats', 'App\Modules\Sales\Controllers\CustomerController@stats');
        add_route('GET', '/sales/customers/mixed', 'App\Modules\Sales\Controllers\CustomerController@mixed');
        add_route('GET', '/sales/customers/dashboard', 'App\Modules\Sales\Controllers\CustomerController@dashboard');
        add_route('GET', '/sales/customers/create', 'App\Modules\Sales\Controllers\CustomerController@create');
        add_route('POST', '/sales/customers/store', 'App\Modules\Sales\Controllers\CustomerController@store');

        // مسارات CSV للعملاء - قبل الروابط المتغيرة
        add_route('GET', '/sales/customers/export', 'App\Modules\Sales\Controllers\CsvController@exportCustomers');
        add_route('GET', '/sales/customers/template', 'App\Modules\Sales\Controllers\CsvController@downloadCustomersTemplate');
        add_route('POST', '/sales/customers/upload', 'App\Modules\Sales\Controllers\CsvController@uploadCustomersFile');
        add_route('POST', '/sales/customers/preview', 'App\Modules\Sales\Controllers\CsvController@previewCustomersImport');
        add_route('POST', '/sales/customers/import', 'App\Modules\Sales\Controllers\CsvController@executeCustomersImport');

        // الروابط المتغيرة أخيراً
        add_route('GET', '/sales/customers/{id}', 'App\Modules\Sales\Controllers\CustomerController@show');
        add_route('GET', '/sales/customers/{id}/edit', 'App\Modules\Sales\Controllers\CustomerController@edit');
        add_route('POST', '/sales/customers/{id}/update', 'App\Modules\Sales\Controllers\CustomerController@update');
        add_route('POST', '/sales/customers/{id}/delete', 'App\Modules\Sales\Controllers\CustomerController@delete');


    }
}