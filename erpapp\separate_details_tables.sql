-- ===================================================================
-- جداول التفاصيل المنفصلة للموردين والعملاء
-- الجدول الأساسي concerned_entities يبقى كما هو للمعلومات المشتركة
-- ===================================================================

-- جدول تفاصيل الموردين المنفصل
CREATE TABLE supplier_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_id INT NOT NULL,                     -- مرتبط بـ concerned_entities.id
    company_id INT NOT NULL,
    
    -- معلومات الشركة
    company_name VARCHAR(150),                  -- اسم الشركة (للمورد)
    contact_person VARCHAR(150),                -- اسم الشخص المسؤول
    email VARCHAR(150),                         -- البريد الإلكتروني للمورد
    
    -- المعلومات القانونية
    tax_number VARCHAR(50),                     -- الرقم الضريبي للمورد
    commercial_register VARCHAR(50),            -- السجل التجاري للمورد
    license_number VARCHAR(50),                 -- رقم الترخيص
    license_expiry DATE,                        -- تاريخ انتهاء الترخيص
    establishment_date DATE,                    -- تاريخ التأسيس
    legal_form ENUM('individual', 'llc', 'corporation', 'partnership', 'other'), -- الشكل القانوني
    
    -- الشروط التجارية
    payment_terms INT DEFAULT 30,               -- شروط الدفع (بالأيام)
    credit_limit DECIMAL(15,2) DEFAULT 0,       -- الحد الائتماني
    discount_rate DECIMAL(5,2) DEFAULT 0,       -- معدل الخصم المتفق عليه
    delivery_time INT,                          -- مدة التسليم (بالأيام)
    minimum_order DECIMAL(15,2),                -- الحد الأدنى للطلب
    currency VARCHAR(3) DEFAULT 'SAR',          -- العملة المتعامل بها
    rating ENUM('A', 'B', 'C', 'D') DEFAULT 'C', -- تقييم المورد
    
    -- ملاحظات إضافية
    internal_notes TEXT,                        -- ملاحظات داخلية
    special_instructions TEXT,                  -- تعليمات خاصة

    -- الفهارس
    UNIQUE KEY unique_entity_id (entity_id),
    INDEX idx_company_id (company_id),
    INDEX idx_email (email),
    INDEX idx_tax_number (tax_number),
    INDEX idx_rating (rating)
);

-- جدول تفاصيل العملاء المنفصل
CREATE TABLE customer_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_id INT NOT NULL,                     -- مرتبط بـ concerned_entities.id
    company_id INT NOT NULL,
    
    -- معلومات الاتصال
    email VARCHAR(150),                         -- البريد الإلكتروني
    
    -- المعلومات القانونية
    tax_number VARCHAR(50),                     -- الرقم الضريبي
    commercial_register VARCHAR(50),            -- السجل التجاري
    customer_type ENUM('individual', 'company', 'government', 'other') DEFAULT 'individual', -- نوع العميل
    
    -- الشروط التجارية
    credit_limit DECIMAL(15,2) DEFAULT 0,       -- الحد الائتماني
    payment_terms INT DEFAULT 0,                -- شروط الدفع (بالأيام)
    discount_rate DECIMAL(5,2) DEFAULT 0,       -- معدل الخصم %
    price_list VARCHAR(50),                     -- قائمة الأسعار المخصصة
    rating ENUM('A', 'B', 'C', 'D') DEFAULT 'C', -- تقييم العميل
    
    -- معلومات إضافية
    sales_rep_id INT,                           -- مندوب المبيعات
    territory VARCHAR(100),                     -- المنطقة الجغرافية
    industry VARCHAR(100),                      -- القطاع/الصناعة
    source VARCHAR(100),                        -- مصدر العميل

    -- الفهارس
    UNIQUE KEY unique_entity_id (entity_id),
    INDEX idx_company_id (company_id),
    INDEX idx_email (email),
    INDEX idx_tax_number (tax_number),
    INDEX idx_customer_type (customer_type),
    INDEX idx_rating (rating),
    INDEX idx_sales_rep_id (sales_rep_id)
);

-- جدول تفاصيل الأصناف المنفصل (للمستقبل)
CREATE TABLE product_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_id INT NOT NULL,                     -- مرتبط بـ concerned_entities.id
    company_id INT NOT NULL,
    
    -- الأسعار
    purchase_price DECIMAL(15,4),               -- سعر الشراء
    selling_price DECIMAL(15,4),                -- سعر البيع
    wholesale_price DECIMAL(15,4),              -- سعر الجملة
    retail_price DECIMAL(15,4),                 -- سعر التجزئة
    cost_price DECIMAL(15,4),                   -- سعر التكلفة
    tax_rate DECIMAL(5,2) DEFAULT 0,            -- معدل الضريبة %
    discount_rate DECIMAL(5,2) DEFAULT 0,       -- معدل الخصم %
    
    -- المخزون
    min_stock DECIMAL(15,4) DEFAULT 0,          -- الحد الأدنى للمخزون
    max_stock DECIMAL(15,4),                    -- الحد الأقصى للمخزون
    reorder_point DECIMAL(15,4),                -- نقطة إعادة الطلب
    current_stock DECIMAL(15,4) DEFAULT 0,      -- المخزون الحالي
    reserved_stock DECIMAL(15,4) DEFAULT 0,     -- المخزون المحجوز
    
    -- معلومات المنتج
    barcode VARCHAR(100),                       -- الباركود
    sku VARCHAR(100),                           -- رمز التخزين SKU
    weight DECIMAL(10,3),                       -- الوزن
    dimensions VARCHAR(100),                    -- الأبعاد (طول×عرض×ارتفاع)
    color VARCHAR(50),                          -- اللون
    size VARCHAR(50),                           -- الحجم
    brand VARCHAR(100),                         -- العلامة التجارية
    model VARCHAR(100),                         -- الموديل
    serial_number VARCHAR(100),                 -- الرقم التسلسلي
    expiry_date DATE,                           -- تاريخ الانتهاء
    manufacturing_date DATE,                    -- تاريخ التصنيع
    warranty_period INT,                        -- فترة الضمان (بالأشهر)
    
    -- خصائص المنتج
    track_serial BOOLEAN DEFAULT FALSE,         -- تتبع الأرقام التسلسلية
    track_expiry BOOLEAN DEFAULT FALSE,         -- تتبع تواريخ الانتهاء
    is_service BOOLEAN DEFAULT FALSE,           -- هل هو خدمة
    is_digital BOOLEAN DEFAULT FALSE,           -- هل هو منتج رقمي

    -- الفهارس
    UNIQUE KEY unique_entity_id (entity_id),
    INDEX idx_company_id (company_id),
    INDEX idx_barcode (barcode),
    INDEX idx_sku (sku),
    INDEX idx_brand (brand)
);

-- جدول تفاصيل الموظفين المنفصل (للمستقبل)
CREATE TABLE employee_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entity_id INT NOT NULL,                     -- مرتبط بـ concerned_entities.id
    company_id INT NOT NULL,
    
    -- معلومات شخصية
    national_id VARCHAR(50),                    -- رقم الهوية
    birth_date DATE,                            -- تاريخ الميلاد
    gender ENUM('male', 'female'),              -- الجنس
    marital_status ENUM('single', 'married', 'divorced', 'widowed'), -- الحالة الاجتماعية
    
    -- معلومات وظيفية
    job_title VARCHAR(100),                     -- المسمى الوظيفي
    department VARCHAR(100),                    -- القسم
    salary DECIMAL(10,2),                       -- الراتب الأساسي
    commission_rate DECIMAL(5,2) DEFAULT 0,     -- معدل العمولة %
    hire_date DATE,                             -- تاريخ التوظيف
    
    -- معلومات الطوارئ
    emergency_contact VARCHAR(150),             -- جهة الاتصال في الطوارئ
    emergency_phone VARCHAR(50),                -- هاتف الطوارئ

    -- الفهارس
    UNIQUE KEY unique_entity_id (entity_id),
    INDEX idx_company_id (company_id),
    INDEX idx_national_id (national_id),
    INDEX idx_department (department),
    INDEX idx_hire_date (hire_date)
);

-- ===================================================================
-- تحديث الجدول الأساسي concerned_entities لإزالة الحقول المنقولة
-- ===================================================================

-- إزالة الحقول الخاصة بالموردين (تم نقلها إلى supplier_details)
ALTER TABLE concerned_entities 
DROP COLUMN S_company_name,
DROP COLUMN S_contact_person,
DROP COLUMN S_email,
DROP COLUMN S_tax_number,
DROP COLUMN S_commercial_register,
DROP COLUMN S_payment_terms,
DROP COLUMN S_credit_limit,
DROP COLUMN S_discount_rate,
DROP COLUMN S_delivery_time,
DROP COLUMN S_minimum_order,
DROP COLUMN S_currency,
DROP COLUMN S_rating,
DROP COLUMN S_license_number,
DROP COLUMN S_license_expiry,
DROP COLUMN S_establishment_date,
DROP COLUMN S_legal_form,
DROP COLUMN S_internal_notes,
DROP COLUMN S_special_instructions;

-- إزالة الحقول الخاصة بالعملاء (تم نقلها إلى customer_details)
ALTER TABLE concerned_entities 
DROP COLUMN C_email,
DROP COLUMN C_tax_number,
DROP COLUMN C_commercial_register,
DROP COLUMN C_credit_limit,
DROP COLUMN C_payment_terms,
DROP COLUMN C_discount_rate,
DROP COLUMN C_customer_type,
DROP COLUMN C_price_list,
DROP COLUMN C_sales_rep_id,
DROP COLUMN C_territory,
DROP COLUMN C_industry,
DROP COLUMN C_source,
DROP COLUMN C_rating;

-- إزالة الحقول الخاصة بالأصناف (تم نقلها إلى product_details)
ALTER TABLE concerned_entities 
DROP COLUMN I_purchase_price,
DROP COLUMN I_selling_price,
DROP COLUMN I_wholesale_price,
DROP COLUMN I_retail_price,
DROP COLUMN I_cost_price,
DROP COLUMN I_tax_rate,
DROP COLUMN I_discount_rate,
DROP COLUMN I_min_stock,
DROP COLUMN I_max_stock,
DROP COLUMN I_reorder_point,
DROP COLUMN I_current_stock,
DROP COLUMN I_reserved_stock,
DROP COLUMN I_barcode,
DROP COLUMN I_sku,
DROP COLUMN I_weight,
DROP COLUMN I_dimensions,
DROP COLUMN I_color,
DROP COLUMN I_size,
DROP COLUMN I_brand,
DROP COLUMN I_model,
DROP COLUMN I_serial_number,
DROP COLUMN I_expiry_date,
DROP COLUMN I_manufacturing_date,
DROP COLUMN I_warranty_period,
DROP COLUMN I_track_serial,
DROP COLUMN I_track_expiry,
DROP COLUMN I_is_service,
DROP COLUMN I_is_digital;

-- إزالة الحقول الخاصة بالموظفين (تم نقلها إلى employee_details)
ALTER TABLE concerned_entities 
DROP COLUMN E_national_id,
DROP COLUMN E_job_title,
DROP COLUMN E_department,
DROP COLUMN E_salary,
DROP COLUMN E_commission_rate,
DROP COLUMN E_hire_date,
DROP COLUMN E_birth_date,
DROP COLUMN E_gender,
DROP COLUMN E_marital_status,
DROP COLUMN E_emergency_contact,
DROP COLUMN E_emergency_phone;
