<?php
/**
 * اختبار شامل لتناسق الوحدات والحقول
 */

// تضمين ملف الإعداد
require_once 'erpapp/config/database.php';

echo "<h1>اختبار تناسق الوحدات والحقول</h1>";

try {
    // 1. اختبار بنية الجداول
    echo "<h2>1. اختبار بنية الجداول</h2>";
    
    // فحص جدول concerned_entities
    $sql = "DESCRIBE concerned_entities";
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>أعمدة جدول concerned_entities:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>المفتاح</th><th>القيمة الافتراضية</th></tr>";
    
    $supplier_fields = [];
    $customer_fields = [];
    $general_fields = [];
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "</tr>";
        
        // تصنيف الحقول
        if (strpos($column['Field'], 'S_') === 0) {
            $supplier_fields[] = $column['Field'];
        } elseif (strpos($column['Field'], 'C_') === 0) {
            $customer_fields[] = $column['Field'];
        } elseif (strpos($column['Field'], 'G_') === 0) {
            $general_fields[] = $column['Field'];
        }
    }
    echo "</table>";
    
    // 2. عرض تصنيف الحقول
    echo "<h2>2. تصنيف الحقول</h2>";
    
    echo "<h3>الحقول العامة (G_*):</h3>";
    echo "<ul>";
    foreach ($general_fields as $field) {
        echo "<li>" . htmlspecialchars($field) . "</li>";
    }
    echo "</ul>";
    
    echo "<h3>حقول الموردين (S_*):</h3>";
    echo "<ul>";
    foreach ($supplier_fields as $field) {
        echo "<li>" . htmlspecialchars($field) . "</li>";
    }
    echo "</ul>";
    
    echo "<h3>حقول العملاء (C_*):</h3>";
    echo "<ul>";
    foreach ($customer_fields as $field) {
        echo "<li>" . htmlspecialchars($field) . "</li>";
    }
    echo "</ul>";
    
    // 3. اختبار الاستعلامات مع أسماء المجموعات
    echo "<h2>3. اختبار الاستعلامات مع أسماء المجموعات</h2>";
    
    // اختبار الموردين
    echo "<h3>الموردين مع أسماء المجموعات:</h3>";
    $sql = "SELECT s.entity_number, s.G_name_ar, s.group_id, g.name_ar as group_name,
                   s.S_company_name, s.S_email
            FROM concerned_entities s
            LEFT JOIN entity_groups g ON s.group_id = g.group_number 
                AND g.company_id = s.company_id AND g.section_id = s.section_id
            WHERE s.section_id = (SELECT id FROM sections WHERE code = 'suppliers')
            LIMIT 5";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $suppliers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($suppliers)) {
        echo "<p style='color: orange;'>لا توجد موردين في قاعدة البيانات</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم المورد</th><th>الاسم</th><th>المجموعة</th><th>اسم الشركة</th><th>البريد الإلكتروني</th></tr>";
        
        foreach ($suppliers as $supplier) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($supplier['entity_number']) . "</td>";
            echo "<td>" . htmlspecialchars($supplier['G_name_ar']) . "</td>";
            echo "<td>" . htmlspecialchars($supplier['group_name'] ?: 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($supplier['S_company_name'] ?: '-') . "</td>";
            echo "<td>" . htmlspecialchars($supplier['S_email'] ?: '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // اختبار العملاء
    echo "<h3>العملاء مع أسماء المجموعات:</h3>";
    $sql = "SELECT s.entity_number, s.G_name_ar, s.group_id, g.name_ar as group_name,
                   s.C_customer_type, s.C_email
            FROM concerned_entities s
            LEFT JOIN entity_groups g ON s.group_id = g.group_number 
                AND g.company_id = s.company_id AND g.section_id = s.section_id
            WHERE s.section_id = (SELECT id FROM sections WHERE code = 'customers')
            LIMIT 5";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $customers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($customers)) {
        echo "<p style='color: orange;'>لا توجد عملاء في قاعدة البيانات</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم العميل</th><th>الاسم</th><th>المجموعة</th><th>نوع العميل</th><th>البريد الإلكتروني</th></tr>";
        
        foreach ($customers as $customer) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($customer['entity_number']) . "</td>";
            echo "<td>" . htmlspecialchars($customer['G_name_ar']) . "</td>";
            echo "<td>" . htmlspecialchars($customer['group_name'] ?: 'غير محدد') . "</td>";
            echo "<td>" . htmlspecialchars($customer['C_customer_type'] ?: '-') . "</td>";
            echo "<td>" . htmlspecialchars($customer['C_email'] ?: '-') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. اختبار المجموعات
    echo "<h2>4. اختبار المجموعات</h2>";
    
    echo "<h3>مجموعات الموردين:</h3>";
    $sql = "SELECT group_number, name_ar, is_default 
            FROM entity_groups 
            WHERE section_id = (SELECT id FROM sections WHERE code = 'suppliers')
            ORDER BY is_default DESC, name_ar";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $supplierGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($supplierGroups)) {
        echo "<p style='color: orange;'>لا توجد مجموعات موردين</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم المجموعة</th><th>اسم المجموعة</th><th>افتراضي</th></tr>";
        
        foreach ($supplierGroups as $group) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($group['group_number']) . "</td>";
            echo "<td>" . htmlspecialchars($group['name_ar']) . "</td>";
            echo "<td>" . ($group['is_default'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h3>مجموعات العملاء:</h3>";
    $sql = "SELECT group_number, name_ar, is_default 
            FROM entity_groups 
            WHERE section_id = (SELECT id FROM sections WHERE code = 'customers')
            ORDER BY is_default DESC, name_ar";
    
    $stmt = $db->prepare($sql);
    $stmt->execute();
    $customerGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($customerGroups)) {
        echo "<p style='color: orange;'>لا توجد مجموعات عملاء</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>رقم المجموعة</th><th>اسم المجموعة</th><th>افتراضي</th></tr>";
        
        foreach ($customerGroups as $group) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($group['group_number']) . "</td>";
            echo "<td>" . htmlspecialchars($group['name_ar']) . "</td>";
            echo "<td>" . ($group['is_default'] ? 'نعم' : 'لا') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. ملخص النتائج
    echo "<h2>5. ملخص النتائج</h2>";
    echo "<div style='background-color: #e8f5e8; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ التصحيحات المطبقة:</h3>";
    echo "<ul>";
    echo "<li>تم إصلاح الـ JOIN في نماذج الموردين والعملاء</li>";
    echo "<li>تم تصحيح استخدام الحقول في نموذج العملاء (C_* بدلاً من S_*)</li>";
    echo "<li>تم إضافة قيم افتراضية لأعمدة المجموعات في ملفات العرض</li>";
    echo "<li>تم توحيد التعليقات والأسماء في جميع الملفات</li>";
    echo "<li>تم إنشاء معايير برمجة موحدة</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 10px;'>";
    echo "<h3>📋 التوصيات:</h3>";
    echo "<ul>";
    echo "<li>اتباع معايير البرمجة الموحدة في ملف CODING_STANDARDS.md</li>";
    echo "<li>استخدام الحقول الصحيحة لكل وحدة (S_* للموردين، C_* للعملاء)</li>";
    echo "<li>التأكد من صحة الـ JOIN عند إضافة استعلامات جديدة</li>";
    echo "<li>اختبار جميع الوظائف بعد أي تعديلات</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background-color: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ خطأ في الاختبار:</h3>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background-color: #f8f9fa;
}

h1, h2, h3 {
    color: #2c3e50;
}

table {
    background-color: white;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

th {
    background-color: #3498db;
    color: white;
    padding: 10px;
    text-align: center;
}

td {
    padding: 8px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

ul {
    background-color: white;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
