/**
 * CSS لنظام استيراد وتصدير CSV
 */

/* نافذة الاستيراد */
.csv-import-modal .modal-dialog {
    max-width: 800px;
}

.csv-import-modal .modal-content {
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.csv-import-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 1.5rem;
}

.csv-import-modal .modal-title {
    font-weight: 600;
    font-size: 1.25rem;
}

.csv-import-modal .modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.csv-import-modal .modal-close:hover {
    opacity: 1;
}

/* خطوات الاستيراد */
.import-step {
    padding: 2rem 0;
}

.step-title {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    font-weight: 600;
    color: #2d3748;
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    font-weight: 700;
    font-size: 0.875rem;
    margin-left: 12px;
}

/* منطقة رفع الملف */
.upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    background: #f7fafc;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.drag-over {
    border-color: #667eea;
    background: #edf2f7;
    transform: translateY(-2px);
}

.upload-icon {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 1rem;
}

.upload-text {
    font-size: 1.125rem;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
}

.upload-hint {
    color: #718096;
    font-size: 0.875rem;
    margin-bottom: 1.5rem;
}

/* شريط التقدم */
.upload-progress {
    margin-top: 1.5rem;
}

.progress {
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: 0.5rem;
    color: #718096;
    font-size: 0.875rem;
}

/* جدول تطابق الأعمدة */
.mapping-container {
    background: #f7fafc;
    border-radius: 8px;
    padding: 1.5rem;
}

.mapping-info {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: white;
    border-radius: 6px;
    border-right: 4px solid #667eea;
}

.mapping-table table {
    background: white;
    border-radius: 6px;
    overflow: hidden;
}

.mapping-table th {
    background: #edf2f7;
    font-weight: 600;
    color: #2d3748;
    padding: 1rem;
}

.mapping-table td {
    padding: 1rem;
    vertical-align: middle;
}

.mapping-select {
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 0.5rem;
    font-size: 0.875rem;
    transition: border-color 0.3s ease;
}

.mapping-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* معاينة البيانات */
.preview-info {
    background: #f0fff4;
    border: 1px solid #9ae6b4;
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.preview-info p {
    margin-bottom: 0.5rem;
    color: #2f855a;
}

.preview-info p:last-child {
    margin-bottom: 0;
}


.table-sm th {
    background: #edf2f7;
    font-weight: 600;
    color: #2d3748;
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table-sm td {
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
}

.table-warning {
    background-color: #fef5e7 !important;
}

/* نتائج الاستيراد */
.results-summary {
    text-align: center;
}

.results-summary .alert {
    border-radius: 8px;
    padding: 1.5rem;
}

.results-summary .alert h6 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.results-summary .alert-success {
    background: #f0fff4;
    border: 1px solid #9ae6b4;
    color: #2f855a;
}

.results-summary .alert-danger {
    background: #fed7d7;
    border: 1px solid #feb2b2;
    color: #c53030;
}

.results-summary .alert-warning {
    background: #fef5e7;
    border: 1px solid #f6e05e;
    color: #d69e2e;
}

/* أزرار الخطوات */
.step-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.step-actions .btn {
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.step-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* أزرار CSV في الهيدر */
.btn.btn-success,
.btn.btn-info,
.btn.btn-warning {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn.btn-success:hover,
.btn.btn-info:hover,
.btn.btn-warning:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .csv-import-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .upload-icon {
        font-size: 2rem;
    }
    
    .step-actions {
        flex-direction: column;
        gap: 1rem;
    }
    
    .mapping-table {
        overflow-x: auto;
    }
}

/* تحسينات للثيم الداكن */
body.dark-theme .csv-import-modal .modal-content {
    background: #2d3748;
    color: #e2e8f0;
}

body.dark-theme .upload-area {
    background: #4a5568;
    border-color: #718096;
}

body.dark-theme .mapping-container {
    background: #4a5568;
}

body.dark-theme .mapping-info {
    background: #2d3748;
    color: #e2e8f0;
}

body.dark-theme .table th {
    background: #4a5568;
    color: #e2e8f0;
}

body.dark-theme .preview-info {
    background: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}
