/* ===== BUTTONS (بديل Bootstrap) ===== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-4);
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-fast) ease;
    gap: var(--spacing-2);
    white-space: nowrap;
    user-select: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* ===== PRIMARY BUTTON ===== */
.btn-primary {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--light-color);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.btn-outline-primary {
    background: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--light-color);
}

/* ===== SUCCESS BUTTON ===== */
.btn-success {
    background: var(--success-color);
    border-color: var(--success-color);
    color: var(--light-color);
}

.btn-success:hover {
    background: var(--success-dark);
    border-color: var(--success-dark);
    color: var(--light-color);
    transform: translateY(-1px);
}

.btn-outline-success {
    background: transparent;
    border-color: var(--success-color);
    color: var(--success-color);
}

.btn-outline-success:hover {
    background: var(--success-color);
    color: var(--light-color);
}

/* ===== WARNING BUTTON ===== */
.btn-warning {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: var(--dark-color);
}

.btn-warning:hover {
    background: var(--warning-dark);
    border-color: var(--warning-dark);
    color: var(--dark-color);
    transform: translateY(-1px);
}

.btn-outline-warning {
    background: transparent;
    border-color: var(--warning-color);
    color: var(--warning-color);
}

.btn-outline-warning:hover {
    background: var(--warning-color);
    color: var(--dark-color);
}

/* ===== DANGER BUTTON ===== */
.btn-danger {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--light-color);
}

.btn-danger:hover {
    background: var(--danger-dark);
    border-color: var(--danger-dark);
    color: var(--light-color);
    transform: translateY(-1px);
}

.btn-outline-danger {
    background: transparent;
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.btn-outline-danger:hover {
    background: var(--danger-color);
    color: var(--light-color);
}

/* ===== INFO BUTTON ===== */
.btn-info {
    background: var(--info-color);
    border-color: var(--info-color);
    color: var(--light-color);
}

.btn-info:hover {
    background: var(--info-dark);
    border-color: var(--info-dark);
    color: var(--light-color);
    transform: translateY(-1px);
}

.btn-outline-info {
    background: transparent;
    border-color: var(--info-color);
    color: var(--info-color);
}

.btn-outline-info:hover {
    background: var(--info-color);
    color: var(--light-color);
}

/* ===== SECONDARY BUTTON ===== */
.btn-secondary {
    background: var(--gray-600);
    border-color: var(--gray-600);
    color: var(--light-color);
}

.btn-secondary:hover {
    background: var(--gray-700);
    border-color: var(--gray-700);
    color: var(--light-color);
    transform: translateY(-1px);
}

.btn-outline-secondary {
    background: transparent;
    border-color: var(--gray-600);
    color: var(--gray-600);
}

.btn-outline-secondary:hover {
    background: var(--gray-600);
    color: var(--light-color);
}

/* ===== LIGHT BUTTON ===== */
.btn-light {
    background: var(--gray-100);
    border-color: var(--gray-100);
    color: var(--gray-800);
}

.btn-light:hover {
    background: var(--gray-200);
    border-color: var(--gray-200);
    color: var(--gray-800);
    transform: translateY(-1px);
}

.btn-outline-light {
    background: transparent;
    border-color: var(--gray-300);
    color: var(--gray-600);
}

.btn-outline-light:hover {
    background: var(--gray-100);
    color: var(--gray-800);
}

/* ===== BUTTON SIZES ===== */
.btn-sm {
    padding: var(--spacing-1) var(--spacing-3);
    font-size: 12px;
}

.btn-lg {
    padding: var(--spacing-3) var(--spacing-6);
    font-size: 16px;
}

/* ===== BUTTON GROUPS ===== */
.btn-group {
    display: inline-flex;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.btn-group .btn {
    border-radius: 0;
    border-right-width: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius);
    border-bottom-left-radius: var(--border-radius);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
    border-right-width: 1px;
}

/* ===== DARK THEME BUTTONS ===== */
body.dark-theme .btn-light {
    background: var(--gray-700);
    border-color: var(--gray-700);
    color: var(--dark-text-color);
}

body.dark-theme .btn-light:hover {
    background: var(--gray-600);
    border-color: var(--gray-600);
}

body.dark-theme .btn-outline-light {
    border-color: var(--gray-600);
    color: var(--gray-300);
}

body.dark-theme .btn-outline-light:hover {
    background: var(--gray-700);
    color: var(--dark-text-color);
}

body.dark-theme .btn-secondary {
    background: var(--gray-600);
    border-color: var(--gray-600);
}

body.dark-theme .btn-secondary:hover {
    background: var(--gray-500);
    border-color: var(--gray-500);
}

/* ===== FLOATING ACTION BUTTON ===== */
.btn-fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    padding: 0;
    box-shadow: var(--box-shadow-lg);
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    z-index: 1000;
}

[dir="rtl"] .btn-fab {
    right: auto;
    left: var(--spacing-6);
}

.btn-fab:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-xl);
}

/* ===== ICON BUTTONS ===== */
.btn-icon {
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: var(--border-radius);
}

.btn-icon-sm {
    width: 32px;
    height: 32px;
    padding: 0;
    border-radius: var(--border-radius-sm);
}

/* ===== LOADING STATE ===== */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ENHANCED BUTTON STYLES ===== */

/* Modern Primary Button with Gradient */
.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
    color: var(--light-color);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(99, 102, 241, 0.4);
}

/* Enhanced Outline Buttons */
.btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    font-weight: 500;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}



.btn-outline-primary:hover {
    color: var(--light-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* Enhanced Success Button */
.btn-success {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
    border: none;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    background: linear-gradient(135deg, var(--success-dark), var(--success-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
}

/* Enhanced Danger Button */
.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), var(--danger-dark));
    border: none;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-dark), var(--danger-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
}

/* Enhanced Warning Button */
.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
    border: none;
    color: var(--dark-color);
    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-dark), var(--warning-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
}

/* Enhanced Info Button */
.btn-info {
    background: linear-gradient(135deg, var(--info-color), var(--info-dark));
    border: none;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.btn-info:hover {
    background: linear-gradient(135deg, var(--info-dark), var(--info-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
}

/* Enhanced Secondary Button */
.btn-secondary {
    background: linear-gradient(135deg, var(--gray-600), var(--gray-700));
    border: none;
    box-shadow: 0 2px 8px rgba(75, 85, 99, 0.3);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, var(--gray-700), var(--gray-600));
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(75, 85, 99, 0.4);
}

/* Button with Icon Spacing */
.btn i + span,
.btn span + i {
    margin-left: var(--spacing-2);
}

[dir="rtl"] .btn i + span,
[dir="rtl"] .btn span + i {
    margin-left: 0;
    margin-right: var(--spacing-2);
}

/* Pulse Animation for Important Buttons */
.btn-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

/* Glass Effect Button */
.btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--light-color);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* Gradient Border Button */
.btn-gradient-border {
    background: var(--light-card-bg);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    color: var(--primary-color);
}

.btn-gradient-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--success-color), var(--warning-color));
    border-radius: inherit;
    z-index: -1;
    margin: -2px;
}

.btn-gradient-border:hover {
    color: var(--light-color);
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
}
