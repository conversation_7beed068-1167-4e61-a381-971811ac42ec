CREATE TABLE sections (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,      -- كود القسم مثل: products, customers, ...
    description TEXT,                      -- وصف القسم وما يعنيه
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE entity_groups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section_id INT NOT NULL,                    -- القسم المرتبط
    company_id INT NOT NULL,                    -- للشركة المالكة

    -- ✅ الرقم الفريد البسيط (داخلي للشركة والقسم)
    group_number INT NOT NULL,                  -- الرقم الفريد داخل الشركة والقسم (هذا ما يظهر في المسارات)

    name_ar VARCHAR(150) NOT NULL,              -- ال<PERSON>س<PERSON> بالعربي
    name_en VARCHAR(150),                       -- الاس<PERSON> بالإنجليزي
    is_default BOOLEAN DEFAULT FALSE,           -- هل هي المجموعة الافتراضية
    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- فهرس فريد لضمان عدم تكرار الرقم داخل نفس الشركة والقسم
    UNIQUE KEY unique_group_number (company_id, section_id, group_number),
    INDEX idx_section_id (section_id),
    INDEX idx_company_id (company_id),
    INDEX idx_default_group (company_id, section_id, is_default)
);


CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,                                -- الشركة المالكة

    supplier_number INT NOT NULL,                           -- رقم المورد داخل الشركة
    section_id INT NOT NULL DEFAULT 3,                      -- رقم القسم (3 = موردين)
    group_id INT,                                           -- المجموعة التابع لها (مثل: مواد خام، خدمات...)

    -- معلومات عامة
    supplier_name_ar VARCHAR(150) NOT NULL,
    supplier_name_en VARCHAR(150),
    description_ar TEXT,
    description_en TEXT,
    phone VARCHAR(50),
    mobile VARCHAR(50),
    website VARCHAR(255),
    image VARCHAR(255),
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',

    -- معلومات الشركة
    company_name VARCHAR(150),
    contact_person VARCHAR(150),
    email VARCHAR(150),

    -- المعلومات القانونية
    tax_number VARCHAR(50),
    commercial_register VARCHAR(50),
    license_number VARCHAR(50),
    license_expiry DATE,
    establishment_date DATE,
    legal_form ENUM('individual', 'llc', 'corporation', 'partnership', 'other'),

    -- الشروط التجارية
    payment_terms INT DEFAULT 30,
    credit_limit DECIMAL(15,2) DEFAULT 0,
    discount_rate DECIMAL(5,2) DEFAULT 0,
    delivery_time INT,
    minimum_order DECIMAL(15,2),
    currency VARCHAR(3) DEFAULT 'SAR',
    rating ENUM('A', 'B', 'C', 'D') DEFAULT 'C',

    -- ملاحظات
    internal_notes TEXT,
    special_instructions TEXT,

    -- النظام
    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- الفهارس
    UNIQUE KEY unique_supplier_number (company_id, supplier_number),
    INDEX idx_company_id (company_id),
    INDEX idx_group_id (group_id),
    INDEX idx_section_id (section_id),
    INDEX idx_status (status),
    INDEX idx_email (email),
    INDEX idx_tax_number (tax_number),
    INDEX idx_rating (rating)
);


CREATE TABLE entity_bank_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,

    -- العلاقة مع الكيان
    entity_type ENUM('supplier', 'customer') NOT NULL,  -- نوع الكيان المرتبط
    entity_id INT NOT NULL,                              -- معرف الكيان
    company_id INT NOT NULL,

    -- معلومات البنك
    bank_name VARCHAR(150) NOT NULL,
    bank_code VARCHAR(20),
    branch_name VARCHAR(150),
    branch_code VARCHAR(20),

    -- معلومات الحساب
    account_number VARCHAR(50) NOT NULL,
    account_name VARCHAR(150) NOT NULL,
    account_type ENUM('checking', 'savings', 'business', 'other') DEFAULT 'checking',

    -- معلومات دولية
    iban VARCHAR(50),
    swift_code VARCHAR(20),
    routing_number VARCHAR(20),

    -- العملة والحالة
    currency VARCHAR(3) DEFAULT 'SAR',
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,

    -- أغراض ومحددات إضافية
    account_purpose ENUM('general', 'payroll', 'tax', 'investment', 'loan', 'other') DEFAULT 'general',
    credit_limit DECIMAL(15,2),
    minimum_balance DECIMAL(15,2),

    -- معلومات الاتصال البنكي
    bank_phone VARCHAR(50),
    bank_email VARCHAR(150),
    bank_address TEXT,

    -- ملاحظات
    notes TEXT,

    -- النظام
    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    -- الفهارس
    UNIQUE KEY unique_account (entity_type, entity_id, bank_name, account_number),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_company_id (company_id),
    INDEX idx_entity_default (entity_type, entity_id, is_default),
    INDEX idx_iban (iban),
    INDEX idx_account_number (account_number)
);
