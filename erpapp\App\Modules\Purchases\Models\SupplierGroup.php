<?php
namespace App\Modules\Purchases\Models;

use PDO;
use Exception;

/**
 * SupplierGroup Model - نموذج مجموعات الموردين
 */
class SupplierGroup
{
    /**
     * Database connection
     */
    protected $db;

    /**
     * Table name
     */
    protected $table = 'entity_groups';

    /**
     * Section ID for suppliers
     */
    protected $section_id;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $db;
        $this->db = $db;
        
        // الحصول على section_id للموردين
        $stmt = $this->db->prepare("SELECT id FROM sections WHERE code = 'suppliers'");
        $stmt->execute();
        $this->section_id = $stmt->fetchColumn();
    }

    /**
     * الحصول على جميع مجموعات الموردين للشركة
     */
    public function getByCompany($company_id, $filters = [])
    {
        $sql = "SELECT * FROM {$this->table} WHERE company_id = ? AND section_id = ?";
        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (!empty($filters['search'])) {
            $sql .= " AND (name_ar LIKE ? OR name_en LIKE ?)";
            $search = '%' . $filters['search'] . '%';
            $params[] = $search;
            $params[] = $search;
        }

        $sql .= " ORDER BY is_default DESC, name_ar";

        // إضافة pagination - استخدام القيم مباشرة في MySQL
        if (isset($filters['limit']) && isset($filters['offset'])) {
            $limit = (int)$filters['limit'];
            $offset = (int)$filters['offset'];
            $sql .= " LIMIT {$limit} OFFSET {$offset}";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على مجموعة بالرقم الداخلي
     */
    public function getByNumber($group_number, $company_id)
    {
        $sql = "SELECT * FROM {$this->table} WHERE group_number = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$group_number, $company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * إنشاء مجموعة موردين جديدة
     */
    public function create($data)
    {
        // الحصول على الرقم التالي
        $nextNumber = $this->getNextNumber($data['company_id']);

        $sql = "INSERT INTO {$this->table} (
                    company_id, section_id, group_number,
                    name_ar, name_en, is_default, created_by, created_at
                ) VALUES (
                    ?, ?, ?, ?, ?, ?, ?, NOW()
                )";

        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            $data['company_id'],
            $this->section_id,
            $nextNumber,
            $data['name_ar'],
            $data['name_en'] ?: null,
            $data['is_default'] ?? 0,
            $data['created_by']
        ]);

        return $result ? $nextNumber : false;
    }

    /**
     * تحديث مجموعة موردين
     */
    public function update($group_number, $data, $company_id)
    {
        $sql = "UPDATE {$this->table} SET
                    name_ar = ?, name_en = ?, updated_by = ?, updated_at = NOW()
                WHERE group_number = ? AND company_id = ? AND section_id = ?";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            $data['name_ar'],
            $data['name_en'] ?: null,
            $data['updated_by'],
            $group_number,
            $company_id,
            $this->section_id
        ]);
    }

    /**
     * حذف مجموعة موردين
     */
    public function delete($group_number, $company_id)
    {
        // التحقق من عدم وجود موردين في هذه المجموعة
        $sql = "SELECT COUNT(*) FROM concerned_entities 
                WHERE group_id = (
                    SELECT id FROM {$this->table} 
                    WHERE group_number = ? AND company_id = ? AND section_id = ?
                ) AND company_id = ? AND section_id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$group_number, $company_id, $this->section_id, $company_id, $this->section_id]);

        if ($stmt->fetchColumn() > 0) {
            throw new Exception('لا يمكن حذف المجموعة لوجود موردين بها');
        }

        // حذف المجموعة
        $sql = "DELETE FROM {$this->table} 
                WHERE group_number = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$group_number, $company_id, $this->section_id]);
    }

    /**
     * الحصول على الرقم التالي للمجموعة
     */
    private function getNextNumber($company_id)
    {
        $sql = "SELECT MAX(group_number) FROM {$this->table} 
                WHERE company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $lastNumber = $stmt->fetchColumn();
        
        return ($lastNumber ?: 0) + 1;
    }

    /**
     * الحصول على عدد المجموعات مع الفلاتر
     */
    public function getCountByCompany($company_id, $filters = [])
    {
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ?";

        $params = [$company_id, $this->section_id];

        // تطبيق الفلاتر
        if (isset($filters['search']) && $filters['search'] !== '') {
            $sql .= " AND (name_ar LIKE ? OR name_en LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return (int)$stmt->fetchColumn();
    }

    /**
     * الحصول على إحصائيات مجموعات الموردين
     */
    public function getStats($company_id)
    {
        $stats = [];

        // إجمالي المجموعات
        $sql = "SELECT COUNT(*) FROM {$this->table}
                WHERE company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        $stats['total_groups'] = $stmt->fetchColumn();

        return $stats;
    }

    /**
     * الحصول على مجموعات الموردين للاختيار
     */
    public function getForSelect($company_id)
    {
        $sql = "SELECT group_number, name_ar, is_default FROM {$this->table}
                WHERE company_id = ? AND section_id = ?
                ORDER BY is_default DESC, name_ar";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * الحصول على المجموعة الافتراضية
     */
    public function getDefaultGroup($company_id)
    {
        $sql = "SELECT group_number, name_ar FROM {$this->table}
                WHERE company_id = ? AND section_id = ? AND is_default = 1
                LIMIT 1";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * تعيين مجموعة كافتراضية
     */
    public function setAsDefault($group_number, $company_id)
    {
        // إزالة الافتراضية من جميع المجموعات
        $sql1 = "UPDATE {$this->table} SET is_default = 0
                 WHERE company_id = ? AND section_id = ?";
        $stmt1 = $this->db->prepare($sql1);
        $stmt1->execute([$company_id, $this->section_id]);

        // تعيين المجموعة المحددة كافتراضية
        $sql2 = "UPDATE {$this->table} SET is_default = 1
                 WHERE group_number = ? AND company_id = ? AND section_id = ?";
        $stmt2 = $this->db->prepare($sql2);
        return $stmt2->execute([$group_number, $company_id, $this->section_id]);
    }

    /**
     * البحث عن مجموعة بالاسم والشركة
     */
    public function findByNameAndCompany($name, $company_id)
    {
        $sql = "SELECT * FROM {$this->table}
                WHERE name_ar = ? AND company_id = ? AND section_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$name, $company_id, $this->section_id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * بدء معاملة قاعدة البيانات
     */
    public function beginTransaction()
    {
        return $this->db->beginTransaction();
    }

    /**
     * تأكيد معاملة قاعدة البيانات
     */
    public function commit()
    {
        return $this->db->commit();
    }

    /**
     * إلغاء معاملة قاعدة البيانات
     */
    public function rollback()
    {
        return $this->db->rollback();
    }

    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getDb()
    {
        return $this->db;
    }
}
